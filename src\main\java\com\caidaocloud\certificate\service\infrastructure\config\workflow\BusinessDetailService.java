package com.caidaocloud.certificate.service.infrastructure.config.workflow;

import com.caidaocloud.certificate.service.certificate.application.service.CeritificateAndEmpHistoryService;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpHistoryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.WfBusinessDataDetailDto;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.workflow.service.IBusinessDetailService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class BusinessDetailService extends IBusinessDetailService {
    @Autowired
    private CeritificateAndEmpHistoryService ceritificateAndEmpHistoryService;

    @Override
    protected Map<String, Object> getBusinessDetail(String businessKey) {
        log.info("BusinessDetailService.getBusinessDetail start businessKey={}", businessKey);
        if (StringUtils.isBlank(businessKey)) {
            return null;
        }
        var userInfo = SecurityUserUtil.getSecurityUserInfo();
        CeritificateAndEmpHistoryDto historyDto = new CeritificateAndEmpHistoryDto();
        historyDto.setBusinessKey(businessKey);
        Map<String, Object> map = ceritificateAndEmpHistoryService.get(historyDto);
        if (map == null || CollectionUtils.isEmpty((List<WfBusinessDataDetailDto>)map.get("detailList"))) {
            log.info("BusinessDetailService.getBusinessDetail wfDetail empty businessKey={}", businessKey);
            return null;
        }
        Map<String, Object> detailMap = Maps.newHashMap();
        for (WfBusinessDataDetailDto detailDto : (List<WfBusinessDataDetailDto>)map.get("detailList")) {
            detailMap.put(detailDto.getKey(), detailDto.getValue());
        }
        log.info("CERTIFICATE-EMP 单据详情:"+detailMap.toString());
        return detailMap;
    }

    @Override
    protected List<String> belongFunCode() {
        return Lists.newArrayList("CERTIFICATE-EMP");
    }
}