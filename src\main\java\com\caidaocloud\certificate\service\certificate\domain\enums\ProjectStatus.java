package com.caidaocloud.certificate.service.certificate.domain.enums;

import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;

public enum ProjectStatus {
    APPLYING("申请中"),
    UNDER_CONSTRUCTION("施工中"),
    COMPLETED("已竣工");

    private String description;

    ProjectStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static  ProjectStatus determineProjectStatus(Long startDate,Long endDate) {
        long now = System.currentTimeMillis();
        long start = startDate == null ? 0 : startDate;
        long end = endDate == null ? DateUtil.MAX_TIMESTAMP : endDate;
        if (now < start) {
            return ProjectStatus.APPLYING;
        }
        else if (now < end) {
            return ProjectStatus.UNDER_CONSTRUCTION;
        }
        else {
            return ProjectStatus.COMPLETED;
        }
    }
}
