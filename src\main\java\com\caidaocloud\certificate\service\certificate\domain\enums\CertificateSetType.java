package com.caidaocloud.certificate.service.certificate.domain.enums;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2025/7/30
 */
public enum CertificateSetType {
	EMP("emp",
			Lists.list(
					new CertificateSetModel("员工任职信息", "entity.hr.EmpWorkInfo",Sets.set("name", "organizeTxt", "postTxt","workno")),
					new CertificateSetModel("员工个人信息", "entity.hr.EmpPrivateInfo")
					)),
	QUA("qua",
			Lists.list(
					new CertificateSetModel("员工任职信息", "entity.hr.EmpWorkInfo",Sets.set("name", "organizeTxt", "postTxt","workno")),
					new CertificateSetModel("员工个人信息", "entity.hr.EmpPrivateInfo")
			)),
	CERTIFICATE("certificate",
			Lists.list(
					new CertificateSetModel("员工任职信息", "entity.hr.EmpWorkInfo", Sets.set("name", "workno")),
					new CertificateSetModel("员工个人信息", "entity.hr.EmpPrivateInfo"),
					new CertificateSetModel("证书人员管理", "entity.certificate.certificateAndEmp")
			),
			Lists.list("entity.hr.EmpWorkInfo@name","entity.hr.EmpWorkInfo@workno","entity.certificate.certificateAndEmp@typeName",
					"entity.certificate.certificateAndEmp@ceritifiCateName","entity.certificate.certificateAndEmp@ceritifiCateCode",
					"entity.certificate.certificateAndEmp@isRegister","entity.certificate.certificateAndEmp@acquiredTime",
					"entity.certificate.certificateAndEmp@expireTime",
					"entity.certificate.certificateAndEmp@registerTime","entity.certificate.certificateAndEmp@registrationNo",
					"entity.certificate.certificateAndEmp@specialty","entity.certificate.certificateAndEmp@issueAuthority",
					"entity.certificate.certificateAndEmp@issueAt","entity.certificate.certificateAndEmp@accessProvince",
					"entity.certificate.certificateAndEmp@remake","entity.certificate.certificateAndEmp@isuse",
					"entity.certificate.certificateAndEmp@useStatus","entity.certificate.certificateAndEmp@status"
					)
	),
	PROJECT_CERTIFICATE("project_certificate", Lists.list(
			Lists.list(
					new CertificateSetModel("员工任职信息", "entity.hr.EmpWorkInfo", Sets.set("name", "workno")),
					new CertificateSetModel("员工个人信息", "entity.hr.EmpPrivateInfo"),
					new CertificateSetModel("证书人员管理", "entity.certificate.certificateAndEmp"))
	),
			Lists.list("entity.hr.EmpWorkInfo@name","entity.hr.EmpWorkInfo@workno","entity.hr.EmpWorkInfo@organizeTxt",
					"entity.hr.EmpWorkInfo@postTxt","entity.certificate.certificateAndEmp@specialty",
					"entity.hr.EmpWorkInfo@ext@shebaodi","entity.certificate.certificateAndEmp@isRegister"
			)
			),
	;

	public String code;
	public List<CertificateSetModel> model;
	public List<String> defaultProperty;

	CertificateSetType(String code, List<CertificateSetModel> model) {
		this.code = code;
		this.model = model;
	}

	CertificateSetType(String code, List<CertificateSetModel> model, List<String> defaultProperty) {
		this.code = code;
		this.model = model;
		this.defaultProperty = defaultProperty;
	}

	public static CertificateSetType getByCode(String code) {
		for (CertificateSetType type : values()) {
			if (type.code.equals(code)) {
				return type;
			}
		}
		throw new IllegalArgumentException("Invalid certificate set type code: " + code);
	}

	@Data
	@AllArgsConstructor
	public static class CertificateSetModel {
		private String name;
		private String identifier;
		private Set<String> required;

		CertificateSetModel(String name, String identifier) {
			this.name = name;
			this.identifier = identifier;
			this.required = new HashSet<>();
		}

	}


}
