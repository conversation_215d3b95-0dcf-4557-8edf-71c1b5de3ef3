package com.caidaocloud.certificate.service.certificate.application.dto;

import java.util.List;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("员工搜索dto")
public class EmpSearchDto extends BasePage {
    @ApiModelProperty("时间轴")
    private Long datetime;
    @ApiModelProperty("关键字")
    private String keyword;
    @ApiModelProperty("员工状态过滤，不等于")
    private Integer empStatusExclude;
    @ApiModelProperty("员工id")
    private List<String> empIds;

    @ApiModelProperty("工号")
    private String workNo;

    @ApiModelProperty("邮箱")
    private String companyEmail;

    @ApiModelProperty("是否只要非离职")
    private Boolean notDepartFlag = false;
}
