package com.caidaocloud.certificate.service.certificate.domain.base.enums;


import com.caidaocloud.hrpaas.paas.match.ConditionNode;
import com.caidaocloud.hrpaas.paas.match.ConditionNodeRelationEnum;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;

import java.util.List;
import java.util.Map;

// 匹配规则树对象
@Data
public class ConditionTree {

    private String id;

    private ConditionNodeRelationEnum relation;

    private List<ConditionNode> children;

    public boolean match(Map<String, String> preEmp) {
        if(ConditionNodeRelationEnum.and.equals(relation)){
            return Sequences.sequence(children).forAll(it->it.match(preEmp));
        }else{
            return Sequences.sequence(children).exists(it->it.match(preEmp));
        }
    }
}