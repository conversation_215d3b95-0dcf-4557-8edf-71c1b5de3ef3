package com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.certificate.service.certificate.domain.entity.EmpCertificateDo;
import com.caidaocloud.certificate.service.certificate.domain.repository.IEmpCertificateRepository;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/15 10:43
 **/
@Repository
public class EmpCertificateRepositoryImpl extends BaseRepositoryImpl<EmpCertificateDo> implements IEmpCertificateRepository {

    @Override
    public List<EmpCertificateDo> query(String empId) {
        return DataQuery.identifier(EmpCertificateDo.IDENTIFIER).specifyLanguage().queryInvisible().decrypt()
                .limit(5000, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
                        .andEq("empid", empId), EmpCertificateDo.class).getItems();
    }
}
