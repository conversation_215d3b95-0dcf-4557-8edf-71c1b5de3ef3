package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/22 14:03
 **/
@ApiModel(description = "证书资格查询条件dto")
@Data
public class QualifiedPerQueryDto extends BasePage {
    private String keywords;
    @ApiModelProperty("员工工号或者姓名")
    private String nameOrNo;
    @ApiModelProperty("任职组织")
    private String organize;
    @ApiModelProperty("失效时间")
    private String expireTime;
    @ApiModelProperty("职务")
    private String job;
    @ApiModelProperty("职级")
    private String jobLevel;
    @ApiModelProperty("岗位")
    private String post;
    @ApiModelProperty("持证数量")
    private String certificateNum;
    @ApiModelProperty("社保地方")
    private Address socialSecurity;
    //下拉选择
    @ApiModelProperty("证书名称")
    private String ceritifiCateName;
    @ApiModelProperty("证书Bid")
    private String ceritifiCateBid;
    @ApiModelProperty("证书id集合")
    private List<String> bids;
    @ApiModelProperty("员工状态")
    private String empStatus;

    @ApiModelProperty("head头")
    private String head;

    private String empId;
    private List<String> empIds;
    private String status;

    private boolean filterAvailable;

}
