package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/6 11:05
 **/
@Data
@ApiModel(description = "员工自助新增列表查询字段")
public class HistoryQueryDto extends BasePage {
    @ApiModelProperty("证书名称")
    private String name;
    @ApiModelProperty("审批状态（0审批中1审批通过2审批拒绝")
    private String approveStatus;
    @ApiModelProperty("申请时间")
    private String  createTime;
    /**
     * 员工id
     */
    private String empId;
}
