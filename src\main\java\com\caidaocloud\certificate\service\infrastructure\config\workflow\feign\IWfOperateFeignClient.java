package com.caidaocloud.certificate.service.infrastructure.config.workflow.feign;

import com.caidaocloud.certificate.service.infrastructure.config.workflow.dto.WfTaskApproveDTO;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        value = "caidaocloud-workflow-service-v2",
        fallback = WfOperateFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "wfOperateFeignClient"
)
public interface IWfOperateFeignClient {
    /**
     * 任务审批
     *
     * @param wfApproveTaskDTO 流程参数
     * @return
     */
    @PostMapping("/api/workflow/v2/operate/task/approve")
    Result approveTask(@RequestBody WfTaskApproveDTO wfApproveTaskDTO);

//    /**
//     * 任务驳回
//     *
//     * @param wfTaskBackDTO 流程参数
//     * @return
//     */
//    @PostMapping("/api/workflow/v2/operate/task/back")
//    Result backTask(@RequestBody WfTaskBackDTO wfTaskBackDTO);
//
//    /**
//     * 检查流程是否已启用
//     *
//     * @param funCode 流程code
//     * @return 布尔
//     */
//    @GetMapping("/api/workflow/v2/config/def/checkEnabled")
//    Result<Boolean> checkDefEnabled(@RequestParam("funCode") String funCode);
//
//    /**
//     * 任务催办
//     *
//     * @param wfTaskUrgeDTO 流程参数
//     * @return
//     */
//    @PostMapping("/api/workflow/v2/urge/task")
//    Result urgeTask(@RequestBody WfTaskUrgeDTO wfTaskUrgeDTO);
//
//    @GetMapping("/api/workflow/v2/view/process/records/get")
//    Result<List<WfProcessRecordDto>> getRecord(@RequestParam(value = "businessKey", required = true) @ApiParam(value = "流程业务key") String businessKey);
}
