package com.caidaocloud.certificate.service.certificate.interfaces.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/17 10:57
 * 人员管理导入类
 **/

@Data
public class CertificateAndEmpExcelVo extends DataEntity {
    @Excel(name = "工号")
    private String workNo;
    @Excel(name = "姓名")
    private String workName;
    @Excel(name = "证书类型名称")
    private String typeName;
    @Excel(name = "证书子类型名称")
    private String typeSubName;
    @Excel(name = "证书名称")
    private String ceritifiCateName;
    @Excel(name = "证书编号")
    private String ceritifiCateCode;
    @Excel(name="取得日期")
    private Long acquiredTime;
    private String acquiredTimeTxt;
    @Excel(name = "注册日期")
    private String registerTime;
    @Excel(name = "失效日期")
    private String expireTime;
    @Excel(name = "注册号")
    private String registrationNo;
    @Excel(name = "专业")
    private String specialty;
    @Excel(name = "签发机构")
    private String issueAuthority;
    @Excel(name = "签发地")
    private String issueAtTxt;
    @Excel(name = "入省备案")
    private String accessProvinceTxt;
    @Excel(name = "是否限制使用")
    private String isuseTxt;
    @Excel(name = "是否注册")
    private String isRegisterTxt;

    @Excel(name = "证书使用状态")
    private String certificateStatus;
    @Excel(name = "状态")
    private String statustxt;

    private DictSimple accessProvince;
    private EnumSimple isuse;
    private EnumSimple isRegister;
    private DictSimple issueAt;
    private String empId;
    private EnumSimple status;
    private EnumSimple useStatus;
    @Excel(name = "备注")
    private String remake;
}
