package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.googlecode.totallylazy.Maps;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/6 10:57
 **/
@Data
@Service
@Slf4j
public class CeritificateAndEmpHistoryDto extends DataEntity {

    private String bid;
    private String identifier;
    private String tenantId;
    private long createTime;
    private String createBy;
    private long updateTime;
    private String updateBy;
    private long dataStartTime;
    private long dataEndTime;
    private boolean deleted;


    @ApiModelProperty("员工id")
    private String empId;

    @ApiModelProperty("员工工号")
    private String workNo;

    @ApiModelProperty("员工姓名")
    private String workName;

    @ApiModelProperty("证书类型")
    private String typeName;

    @ApiModelProperty("证书类型bid")
    private String typeBid;

    @ApiModelProperty("证书子类类型")
    private String typeSubName;

    @ApiModelProperty("证书类型bid")
    private String typeSubBid;

    @ApiModelProperty("证书名称")
    private String ceritifiCateName;

    @ApiModelProperty("证书编码")
    private String ceritifiCateCode;

    @ApiModelProperty("证书id")
    private String ceritifiCateBid;
    @ApiModelProperty("取得日期")
    private Long acquiredTime;
    @ApiModelProperty("注册日期")
    private String registerTime;

    @ApiModelProperty("失效日期")
    private String expireTime;

    @ApiModelProperty("注册号")
    private String registrationNo;

    @ApiModelProperty("专业")
    private String specialty;

    @ApiModelProperty("专业多语言字段")
    private Map<String,Object> i18nName;

    @ApiModelProperty("签发地")
    private DictSimple issueAt;
    private String issueAuthority;

    @ApiModelProperty("入省备案")
    private DictSimple accessProvince;

    @ApiModelProperty("是否限制使用")
    private EnumSimple isuse;

    @ApiModelProperty("是否注册")
    private EnumSimple isRegister;

    @ApiModelProperty("备注")
    private String remake;

    @ApiModelProperty("附件")
    private Attachment file;

    @ApiModelProperty("项目")
    private String proBid;

    @ApiModelProperty("组织id")
    private String organizeId;

    @ApiModelProperty("审批状态")
    private EnumSimple approveStatus;


    @ApiModelProperty("表单id")
    private String formId;

    @ApiModelProperty("表单数据id")
    private String formData;

    @ApiModelProperty("表单数据")
    private Map<String,Object> formDataMap = Maps.map();

    @ApiModelProperty("流程key")
    private String businessKey;

}
