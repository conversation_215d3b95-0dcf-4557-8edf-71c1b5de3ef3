package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import java.io.Serializable;

import com.caidaocloud.dto.BasePage;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 * 证书类型查询dto
 **/
@Data
@Slf4j
@NoArgsConstructor
public class CertificateUsageRecordQueryDto extends BasePage {
    private String projectId;

    public CertificateUsageRecordQueryDto(String projectId, int pageNo, int pageSize) {
        this.projectId = projectId;
        setPageNo(pageNo);
        setPageSize(pageSize);
    }

}
