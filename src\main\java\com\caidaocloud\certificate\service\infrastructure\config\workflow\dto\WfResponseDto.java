package com.caidaocloud.certificate.service.infrastructure.config.workflow.dto;

import com.caidaocloud.certificate.service.certificate.interfaces.dto.WfBusinessDataDetailDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class WfResponseDto {
    @ApiModelProperty("详情列表")
    private List<WfBusinessDataDetailDto> detailList;
//    @ApiModelProperty("销假单详情")
//    private List<LeaveTimeWfDetailDto> leaveCancelList;
}
