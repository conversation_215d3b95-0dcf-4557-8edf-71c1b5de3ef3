package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import groovy.util.logging.Slf4j;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 **/
@Data
@Slf4j
public class CertificateTypeDto  implements Serializable   {
    @ApiModelProperty("证书类型ID")
    private String bid;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("名称")
    private Map<String,Object> i18nName;
    @ApiModelProperty("编码")
    private String code;
    @ApiModelProperty("层级")
    private Integer level;
    @ApiModelProperty("序列编码")
    private Integer sortNum;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("父级bid")
    private String pBid;
}
