package com.caidaocloud.certificate.service.certificate.domain.enums;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

/**
 * 证书有效状态枚举
 */
public enum CertificateStatusEnum {
    
    /**
     * 有效
     */
    VALID("0", "有效"),
    
    /**
     * 无效
     */
    INVALID("1", "无效");
    
    private final String value;
    private final String text;
    
    CertificateStatusEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }
    
    public String getValue() {
        return value;
    }
    
    public String getText() {
        return text;
    }
    
    /**
     * 创建EnumSimple对象
     */
    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(this.value);
        enumSimple.setText(this.text);
        return enumSimple;
    }
    
    /**
     * 根据值获取枚举
     */
    public static CertificateStatusEnum fromValue(String value) {
        for (CertificateStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return VALID; // 默认返回有效
    }
}
