package com.caidaocloud.certificate.service.certificate.interfaces.vo;

import com.caidaocloud.certificate.service.certificate.application.dto.EmpInfoDto;
import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.masterdata.entity.emp.EmpInfoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/15 13:35
 *  证书人员管理列表展示类
 **/
@Data
public class CeritificateAndEmpVo  {
    @ApiModelProperty("入职日期")
    private Long hireDate;
    @ApiModelProperty("职务")
    private String job;
    @ApiModelProperty("职务id")
    private String jobId;
    @ApiModelProperty("社保地")
    private List socialSecurity;
    @ApiModelProperty("社保地txt")
    private String socialSecurityTxt;
    @ApiModelProperty("职级")
    private String jobLevel;
    @ApiModelProperty("职级")
    private String jobLevelId;
    @ApiModelProperty("员工姓名")
    private String name;
    @ApiModelProperty("员工工号")
    private String workno;
    @ApiModelProperty("任职组织")
    private String organize;
    @ApiModelProperty("岗位")
    private String post;
    @ApiModelProperty("任职组织")
    private String organizeId;
    @ApiModelProperty("岗位")
    private String postId;
    @ApiModelProperty("头像")
    private Attachment photo;
    @ApiModelProperty("员工工龄")
    private Integer workAge;
    @ApiModelProperty("bid")
    private String bid;
    @ApiModelProperty("员工bid")
    private String empId;
    @ApiModelProperty("员工状态")
    private EnumSimple empStatus;

    @ApiModelProperty("持有证书数量")
    private Integer CertificateNums;
    @ApiModelProperty("所属所有证书")
    private List<CertificateForEmpVo> certificateVo;

}
