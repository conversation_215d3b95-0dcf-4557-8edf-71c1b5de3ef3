package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 * 证书
 **/
@Data
@Slf4j
public class CertificateDto implements Serializable   {
    @ApiModelProperty("证书ID")
    private String bid;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("证书名称多语言")
    private Map<String, Object> i18nName;
    @ApiModelProperty("编码")
    private String code;
    @ApiModelProperty("序列编码")
    private Integer sortNum;
    @ApiModelProperty("状态")
    private EnumSimple status;
    @ApiModelProperty("类型bid")
    private String typeBid;
    @ApiModelProperty("员工id")
    private String empId;
    @ApiModelProperty("项目id")
    private String proBid;
}
