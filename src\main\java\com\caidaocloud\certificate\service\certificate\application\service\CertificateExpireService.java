package com.caidaocloud.certificate.service.certificate.application.service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.certificate.service.certificate.application.dto.MsgBusinessConfigDto;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateTypeDo;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICeritificateAndEmpRepository;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateRepository;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateTypeRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.core.feign.ScheduleFeignClient;
import com.caidaocloud.hr.service.dto.schedule.ScheduleTaskDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SysParamDictDto;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.enums.NotificationMethodEnum;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.WebUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/5/14
 */
@Service
public class CertificateExpireService {
	@Resource
	private ICeritificateAndEmpRepository ceritificateAndEmpRepository;
	@Resource
	private ICertificateRepository certificateRepository;
	@Resource
	private ICertificateTypeRepository certificateTypeRepository;
	@Resource
	private CacheService cacheService;
	@Resource
	private MsgNoticeService msgNoticeService;
	@Resource
	private ScheduleFeignClient scheduleFeignClient;


	private final int PAGE_SIZE = 500;
	public void notice(List<MsgConfigDto> msgConfigList, List<MsgBusinessConfigDto> businessConfigList, long currentTimestamp) {
		Set<MsgBusinessConfigDto> businessConfigSet = Sequences.sequence(businessConfigList).toSet();
		int pageNo = 1;
		while (true) {
			PageResult<CeritificateAndEmpDo> result = ceritificateAndEmpRepository.queryPage(new CeritificateAndEmpQueryDto(), PAGE_SIZE, pageNo);
			if (result.getItems().isEmpty()) {
				break;
			}
			for (CeritificateAndEmpDo data : result.getItems()) {
				if (StringUtils.isEmpty(data.getExpireTime())) {
					continue;
				}
				for (MsgConfigDto configDto : msgConfigList) {
					if (!businessConfigSet.contains(new MsgBusinessConfigDto(data.getCeritifiCateBid(), configDto.getBid()))) {
						continue;
					}
					// 兼容失效时间为23:59:59
					long expireTime = com.caidaocloud.certificate.service.certificate.infrastructure.util.DateUtil.getTimestampOfDayStart(Long.valueOf(data.getExpireTime()));
					if (!configDto.checkMsgConfig(expireTime, currentTimestamp)) {
						continue;
					}
					Map<String, String> ext = buildMsgParameter(data);
					doNotify(configDto,currentTimestamp, data.getEmpId(), ext);
				}
			}
			pageNo+=1;

		}

	}

	private void doNotify(MsgConfigDto msgConfig, long currentTimestamp, String empId, Map<String, String> ext) {
		// 发送时间为空,直接发送
		msgNoticeService.sendMsgNoticeEvent(msgConfig.getBid(), Lists.list(empId), ext, "hr", 0);
		// Integer configTime = msgConfig.getFunc() == NotificationMethodEnum.MERGE ? msgConfig.getMergeRule().getMergeTime() : msgConfig.getSendTime();
		// if (configTime == null) {
		// 	msgNoticeService.sendMsgNoticeEvent(msgConfig.getBid(), Lists.list(empId), ext, "hr", 0);
		// 	return;
		// }
		// // 发送时间小于当前时间
		// long sendTime = currentTimestamp + configTime;
		// if (sendTime <= System.currentTimeMillis()) {
		// 	return;
		// }
	}


	private Map<String, String> buildMsgParameter(CeritificateAndEmpDo data) {
		Map<String, String> map = new HashMap<>();
		CertificateTypeDo type = certificateTypeRepository.selectById(data.getTypeBid(), "entity.certificate.certificateType");
		CertificateTypeDo subType = certificateTypeRepository.selectById(data.getTypeSubBid(), "entity.certificate.certificateType");
		CertificateDo certificate = certificateRepository.selectById(data.getCeritifiCateBid(), "entity.certificate.certificate");
		Map<String, String> typeI18nName = type != null && type.getI18nName()!=null ? FastjsonUtil.toObject(type.getI18nName(), Map.class) : new HashMap<>();
		Map<String, String> subTypeI18nName = subType != null && subType.getI18nName()!=null ? FastjsonUtil.toObject(subType.getI18nName(), Map.class) : new HashMap<>();
		Map<String, String> certificateI18nName = certificate != null && certificate.getI18nName()!=null ? FastjsonUtil.toObject(certificate.getI18nName(), Map.class) : new HashMap<>();

		// 证书类型
		map.put("certificate.type.name.cn", typeI18nName.getOrDefault(Locale.CHINA.toString(), typeI18nName.get("default")));
		map.put("certificate.type.name.jp", typeI18nName.getOrDefault(Locale.JAPAN.toString(), typeI18nName.get("default")));
		map.put("certificate.subtype.name.cn", subTypeI18nName.getOrDefault(Locale.CHINA.toString(), subTypeI18nName.get("default")));
		map.put("certificate.subtype.name.jp", subTypeI18nName.getOrDefault(Locale.JAPAN.toString(), subTypeI18nName.get("default")));
		map.put("certificate.certificate.name.cn", certificateI18nName.getOrDefault(Locale.CHINA.toString(), certificateI18nName.get("default")));
		map.put("certificate.certificate.name.jp", certificateI18nName.getOrDefault(Locale.JAPAN.toString(), certificateI18nName.get("default")));

		//证书编号
		map.put("certificate.ceritifiCateCode", data.getCeritifiCateCode());
		map.put("certificate.registered", data.getIsRegister().getText());

		//证书注册、失效日期
		if (StringUtils.isNotEmpty(data.getRegisterTime())) {
			map.put("certificate.registerTime", DateUtil.formatDate(Long.valueOf(data.getRegisterTime())));
		}
		if (StringUtils.isNotEmpty(data.getExpireTime())) {
			map.put("certificate.expireTime", DateUtil.formatDate(Long.valueOf(data.getExpireTime())));
		}
		map.put("certificate.registrationNo", data.getRegistrationNo());

		// 专业
		Map<String, String> specialtyI18nName = data.getI18nName()!=null ? FastjsonUtil.toObject(data.getI18nName(), Map.class) : new HashMap<>();
		map.put("certificate.specialty.cn", specialtyI18nName.getOrDefault(Locale.CHINA.toString(), specialtyI18nName.get("default")));
		map.put("certificate.specialty.jp", specialtyI18nName.getOrDefault(Locale.JAPAN.toString(), specialtyI18nName.get("default")));

		// 签发地、入省备案
		SysParamDictDto dictDto = FastjsonUtil.toObject(cacheService.getValue(String.format("DICT_%s", data.getIssueAt().getValue())), SysParamDictDto.class);
		if (dictDto != null) {
			map.put("certificate.issueAt.cn", dictDto.getDictChnName());
			Map langMap = FastjsonUtil.convertObject(dictDto.getDictNameLang(), Map.class);
			map.put("certificate.issueAt.jp",(String) langMap.getOrDefault("ja",dictDto.getDictChnName()));
		}
		dictDto = FastjsonUtil.toObject(cacheService.getValue(String.format("DICT_%s", data.getAccessProvince().getValue())), SysParamDictDto.class);
		if (dictDto != null) {
			map.put("certificate.accessProvince.cn", dictDto.getDictChnName());
			Map langMap = FastjsonUtil.convertObject(dictDto.getDictNameLang(), Map.class);
			map.put("certificate.accessProvince.jp", (String) langMap.getOrDefault("ja", dictDto.getDictChnName()));
		}

		//限制使用
		map.put("certificate.limited", data.getIsuse().getText());
		// 证书使用状态
		data.checkActive(System.currentTimeMillis());
		map.put("certificate.status", data.getStatus().getText());

		return map;
	}
}
