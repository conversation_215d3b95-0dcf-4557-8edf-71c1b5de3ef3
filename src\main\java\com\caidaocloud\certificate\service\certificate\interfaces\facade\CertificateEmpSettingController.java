package com.caidaocloud.certificate.service.certificate.interfaces.facade;

import com.caidaocloud.certificate.service.certificate.application.service.CertificateEmpSettingService;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateEmpDo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/20 16:15
 * 废弃
 **/
@Deprecated
@RestController
@RequestMapping("/api/certificate/v1/set/emp")
@Api(tags = "证书人员设置设置接口")
@Slf4j
public class CertificateEmpSettingController {

    @Resource
    private CertificateEmpSettingService CertificateEmpSettingService;
    @PostMapping("/list")
    @ApiOperation("列表")
    public Result selectList(@RequestBody CertificateEmpDo dto){
        List<CertificateEmpDo> list = CertificateEmpSettingService.selectList(dto);
        return Result.ok(list);
    }

    @PostMapping("/add")
    @ApiOperation("新增")
    public Result add(@RequestBody List<CertificateEmpDo> dto){
        CertificateEmpSettingService.add(dto);
        return Result.ok();
    }

    @PostMapping("/del")
    @ApiOperation("删除")
    public Result del(@RequestBody CertificateEmpDo dto){
        CertificateEmpSettingService.del(dto);
        return Result.ok();
    }


}
