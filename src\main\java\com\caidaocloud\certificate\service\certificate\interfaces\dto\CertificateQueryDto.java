package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 * 证书查询dto
 **/
@Data
@Slf4j
public class CertificateQueryDto extends BasePage implements Serializable   {
    @ApiModelProperty("名称/编码")
    private String nameOrCode;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("bid")
    private String bid;
    @ApiModelProperty("类型bid")
    private String typeBid;
    @ApiModelProperty("员工id")
    private String empId;

    @ApiModelProperty("员工ids")
    private List<String> empIds;


}
