package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateStatus;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import groovy.util.logging.Slf4j;
import lombok.Data;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 * 证书类型查询dto
 **/
@Data
@Slf4j
public class CertificateUsageRecordShowDto {
    private String bid;
    private String projectId;
    private String certificate;
    private DictSimple type;
    private Long startDate;
    private Long endDate;
    private String empId;
    private String projectName;
    private String empName;
    private String empWorkNo;


    public void display(){
        if (startDate==0) {
            startDate = null;
        }
        if (endDate== DateUtil.MAX_TIMESTAMP) {
            endDate = null;
        }
    }
}
