package com.caidaocloud.certificate.service.certificate.application.event;

import com.caidaocloud.hrpaas.metadata.sdk.event.AbstractInteriorEvent;
import com.caidaocloud.hrpaas.paas.common.event.FormPublishEvent;
import com.caidaocloud.msg.handler.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/5/15
 */
@Data
@Slf4j
public class CertificateAcquiredEvent extends AbstractInteriorEvent {
	public static final String TOPIC = "Certificate_Acquired";
	private String tenantId;
	// 人员证书id
	private String empCertificate;

	public CertificateAcquiredEvent(String tenantId, String empCertificate) {
		super(TOPIC);
		this.tenantId = tenantId;
		this.empCertificate = empCertificate;
	}

}
