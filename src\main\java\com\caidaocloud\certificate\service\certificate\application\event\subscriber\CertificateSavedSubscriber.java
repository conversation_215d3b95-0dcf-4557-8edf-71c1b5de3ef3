package com.caidaocloud.certificate.service.certificate.application.event.subscriber;

import java.util.Optional;

import com.caidaocloud.certificate.service.certificate.application.event.CertificateAcquiredEvent;
import com.caidaocloud.certificate.service.certificate.application.event.CertificateSavedEvent;
import com.caidaocloud.certificate.service.certificate.application.service.CeritificateAndEmpService;
import com.caidaocloud.certificate.service.certificate.application.service.CertificateEmpOverviewService;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateUsageRecordDo;
import com.caidaocloud.msg.handler.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/5/15
 */
// @Component
@Slf4j
@Deprecated
public class CertificateSavedSubscriber implements MessageHandler<CertificateSavedEvent> {
	@Autowired
	private CertificateEmpOverviewService certificateEmpOverviewService;

	@Override
	public String topic() {
		return CertificateSavedEvent.TOPIC;
	}

	@Override
	public void handle(CertificateSavedEvent message) throws Exception {
		log.info("CertificateSavedEvent handle,msg={}", message);
		try {
			SecurityUserInfo userInfo = new SecurityUserInfo();
			userInfo.setTenantId(message.getTenantId());
			userInfo.setUserId(0L);
			userInfo.setEmpId(0L);
			SecurityUserUtil.setSecurityUserInfo(userInfo);


			certificateEmpOverviewService.saveOverview(message.getEmpId());
		}
		finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
		log.info("CertificateSavedEvent handle end");
	}
}
