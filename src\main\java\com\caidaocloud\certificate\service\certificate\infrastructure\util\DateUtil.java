package com.caidaocloud.certificate.service.certificate.infrastructure.util;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

/**
 *
 * <AUTHOR>
 * @date 2024/7/17
 */
public class DateUtil {
	public static long getTimestampOfDayStart(long timestamp){
		Instant instant = Instant.ofEpochMilli(timestamp);
		ZoneOffset offset = OffsetDateTime.now().getOffset();
		LocalDateTime time = LocalDateTime.ofInstant(instant, offset);
		return time.withHour(0).withMinute(0).withSecond(0).toEpochSecond(offset) * 1000L;
	}

}
