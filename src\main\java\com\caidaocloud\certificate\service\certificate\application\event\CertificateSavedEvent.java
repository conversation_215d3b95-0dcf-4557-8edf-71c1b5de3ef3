package com.caidaocloud.certificate.service.certificate.application.event;

import com.caidaocloud.hrpaas.metadata.sdk.event.AbstractInteriorEvent;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @date 2024/5/15
 */
@Data
@Slf4j
public class CertificateSavedEvent extends AbstractInteriorEvent {
	public static final String TOPIC = "Certificate_Saved";
	private String tenantId;
	// 人员证书id
	private String empCertificate;
	private String empId;

	public CertificateSavedEvent(String tenantId, String empCertificate, String empId) {
		super(TOPIC);
		this.tenantId = tenantId;
		this.empCertificate = empCertificate;
		this.empId = empId;
	}

}
