package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel(description = "证书使用情况导入dto")
@Data
public class CertificateUsageRecordImportDto {
	private String certificate;
	private DictSimple type;
	private Long startDate;
	private Long endDate;
	private String projectId;
	private EmpSimple emp;
	private String remark;
}

