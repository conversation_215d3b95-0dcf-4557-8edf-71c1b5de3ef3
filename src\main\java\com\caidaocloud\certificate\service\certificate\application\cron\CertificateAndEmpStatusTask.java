package com.caidaocloud.certificate.service.certificate.application.cron;

import com.caidaocloud.certificate.service.certificate.application.dto.TenantDto;
import com.caidaocloud.certificate.service.certificate.application.feign.MaintenanceFeignClient;
import com.caidaocloud.certificate.service.certificate.application.service.CeritificateAndEmpService;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.BeanUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 证书人员状态检测任务
 * <AUTHOR>
 * @date 2024/7/16
 */
@Component
@Slf4j
public class CertificateAndEmpStatusTask {
	@Resource
	private MaintenanceFeignClient maintenanceFeignClient;
	@Resource
	private CeritificateAndEmpService ceritificateAndEmpService;

	/**
	 * 任务执行逻辑
	 * @param jobParam 任务参数（可选）
	 * @return 执行结果
	 * @throws Exception 可能抛出的异常
	 */
	@XxlJob("certificateAndEmpStatusJobHandler")
	public ReturnT<String> execute(String jobParam) throws Exception {
		// 检测证书状态的逻辑
		log.info("certificateAndEmpStatusJobHandler start");
		for (TenantDto tenantDto : maintenanceFeignClient.tenantList().getData()) {
			try {
				SecurityUserInfo userInfo = new SecurityUserInfo();
				userInfo.setTenantId(tenantDto.getTenantId());
				userInfo.setUserId(0L);
				userInfo.setEmpId(0L);
				SecurityUserUtil.setSecurityUserInfo(userInfo);
				log.info("certificateAndEmpStatusJobHandler start,tenantID={}", tenantDto.getTenantId());

				CeritificateAndEmpQueryDto dto=new CeritificateAndEmpQueryDto();
				int pageNo =1;
				while (true) {
					List<CeritificateAndEmpDo> page = ceritificateAndEmpService.queryPage(dto, 1000, pageNo++);
					if (page.isEmpty()) {
						break;
					}
					for (CeritificateAndEmpDo data : page) {
						// CeritificateAndEmpDto empDto = BeanUtil.convert(data, CeritificateAndEmpDto.class);
						ceritificateAndEmpService.flush2(data);
					}
				}
				//初始化证书数量
				ceritificateAndEmpService.initCertificateNums();
			}
			catch (Exception e) {
				log.error("XxlJob certificateAndEmpStatusJobHandler error,tenantId={}", tenantDto.getTenantId(), e );
			}
			finally {
				SecurityUserUtil.removeSecurityUserInfo();
			}
		}

		log.info("certificateAndEmpStatusJobHandler finished");

		// 返回执行结果，0为成功，非0为失败
		return ReturnT.SUCCESS;
	}

}