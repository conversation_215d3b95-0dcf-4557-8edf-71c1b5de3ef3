package com.caidaocloud.certificate.service.certificate.interfaces.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateStatus;
import com.caidaocloud.certificate.service.certificate.domain.enums.ProjectStatus;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/4/15
 */
@Data
@ApiModel(description = "证书项目数据传输对象Vo")
public class CertificateProjectPageVo {

	@ApiModelProperty(notes = "项目ID")
	private String bid;

	@ApiModelProperty(notes = "组织ID")
	private String organize;

	@ApiModelProperty(notes = "项目名称")
	@Excel(name = "项目名称")
	private String projectName;

	@ApiModelProperty(notes = "项目负责人")
	@Excel(name = "项目负责人")
	private EmpSimple emp;

	@ApiModelProperty(notes = "项目地点")
	private String location;

	@ApiModelProperty(notes = "项目地点名称")
	@Excel(name = "项目所在地")
	private String locationTxt;

	@ApiModelProperty(notes = "项目开始日期（毫秒时间戳）")
	@Excel(name = "开工日期")
	private Long startDate;

	@ApiModelProperty(notes = "项目结束日期（毫秒时间戳）")
	@Excel(name = "竣工日期")
	private Long endDate;

	@ApiModelProperty(notes = "项目状态")
	@Excel(name = "项目状态")
	private ProjectStatus projectStatus;

	@ApiModelProperty(notes = "证书开始日期（毫秒时间戳）")
	@Excel(name="证书登记使用日期")
	private Long certificateStartDate;

	@ApiModelProperty(notes = "证书结束日期（毫秒时间戳）")
	@Excel(name="证书结束使用日期")
	private Long certificateEndDate;

	@ApiModelProperty(notes = "证书登记状态")
	@Excel(name = "证书登记状态")
	private CertificateStatus certificateStatus;
}
