package com.caidaocloud.certificate.service.certificate.domain.repository;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepository;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateQueryDto;
import com.caidaocloud.dto.PageResult;

import java.util.List;

/**
 * 证书
 */
public interface ICertificateRepository extends BaseRepository<CertificateDo> {

    /**
     * 证书列表查询
     * @param basePage
     * @param identifier
     * @return
     */
    List<CertificateDo> selectList(CertificateQueryDto basePage, String identifier);

    PageResult<CertificateDo> sortPageList(CertificateQueryDto queryDto, String identifier);

	List<CertificateDo> selectListByIds(List<String> collect);

	List<CertificateDo> all();

}
