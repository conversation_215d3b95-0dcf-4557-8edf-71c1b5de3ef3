package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.certificate.service.certificate.interfaces.vo.CeritificateTopVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/22 14:14
 **/
@ApiModel(description = "设置证书头保存dto")
@Data
public class SetTopSaveDto {
    //用户id
    private String empId;
    //证书名称集合
    private List<CeritificateTopVo> certificateNames;

    private String json;
}
