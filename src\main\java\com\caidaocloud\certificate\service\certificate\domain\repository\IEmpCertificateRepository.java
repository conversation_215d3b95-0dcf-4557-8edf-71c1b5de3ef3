package com.caidaocloud.certificate.service.certificate.domain.repository;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepository;
import com.caidaocloud.certificate.service.certificate.domain.entity.EmpCertificateDo;

import java.util.List;

public interface IEmpCertificateRepository extends BaseRepository<EmpCertificateDo> {


    List<EmpCertificateDo> query(String empId);
}
