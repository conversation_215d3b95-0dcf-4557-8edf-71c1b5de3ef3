package com.caidaocloud.certificate.service.certificate.interfaces.facade;

import com.caidaocloud.certificate.service.certificate.application.service.CeritificateAndEmpHistoryService;
import com.caidaocloud.certificate.service.certificate.domain.base.util.LangUtil;
import com.caidaocloud.certificate.service.certificate.domain.base.util.ObjectConvertUtil;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpHistoryDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpHistoryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.HistoryQueryDto;
import com.caidaocloud.certificate.service.infrastructure.config.workflow.dto.WfApprovalDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.paas.common.feign.FormFeignClient;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/6 11:35
 * Describe : 证书人员工作流历史记录
 **/
@RestController
@RequestMapping("/api/certificate/v1/history")
@Api(value = "/api/certificate/v1/history", description = "证书人员工作流历史记录")
@Slf4j
public class CeritificateAndEmpHistoryController {
    @Autowired
    private CeritificateAndEmpHistoryService historyService;

    @PostMapping("/list")
    @ApiOperation("证书概览")
    public Result<PageResult<CeritificateAndEmpHistoryDto>>selectList(@RequestBody HistoryQueryDto vo){
        String userId = UserContext.getStaffId().toString();
        vo.setEmpId(userId);
        PageResult<CeritificateAndEmpHistoryDo> list = historyService.selectList(vo);
        List<CeritificateAndEmpHistoryDto> datavo = ObjectConvertUtil.convertList(list.getItems(), CeritificateAndEmpHistoryDto.class,
                (it, v1) -> {
                    v1.setI18nName(FastjsonUtil.toObject(it.getI18nName(), Map.class));
                });
        return  Result.ok(new PageResult<>(datavo,vo.getPageNo(),vo.getPageSize(),list.getTotal()));
    }

    @PostMapping("/save")
    @ApiOperation("保存")
    public Result save(@RequestBody CeritificateAndEmpHistoryDto vo){
        vo.setEmpId(String.valueOf(UserContext.getStaffId()));
        return Result.ok(historyService.save(vo));
    }

    @PostMapping("/update")
    @ApiOperation("更新")
    public Result update(@RequestBody CeritificateAndEmpHistoryDto vo){
        CeritificateAndEmpHistoryDo data = ObjectConvertUtil.convert(vo, CeritificateAndEmpHistoryDo.class,(v1,t1)->{
            t1.setAttachFile(v1.getFile());
        });
        data.setI18nName(LangUtil.getI18nValue(vo.getSpecialty(), vo.getI18nName()));
//        vo.setEmpId(String.valueOf(UserContext.getStaffId()));
        return Result.ok(historyService.update(data));
    }

    @PostMapping("/get")
    @ApiOperation("详情接口")
    public Result get(@RequestBody CeritificateAndEmpHistoryDto vo){

        return Result.ok(historyService.get(vo));
    }

    @ApiOperation("工作流回调")
    @PostMapping("/success")
    public Result success(@RequestBody WfApprovalDto dto) {
        log.info("contract change callback data={}", FastjsonUtil.toJson(dto));
        dto.setChoice("APPROVE");
        historyService.callback1(dto);
        return Result.ok(true);
    }
    @ApiOperation("工作流回调")
    @PostMapping("/fail")
    public Result fail(@RequestBody WfApprovalDto dto) {
        log.info("contract change callback data={}", FastjsonUtil.toJson(dto));
        dto.setChoice("REFUSE");
        historyService.callback1(dto);
        return Result.ok(true);
    }

    /**
     * 工作流回调-审批通过
     * @param dto
     * @return
     */
    @ApiOperation("审批通过")
    @PostMapping("approved")
    public Result approved(@RequestBody WfApprovalDto dto) {
        log.info("Work flow approved callback,{}", dto);
        dto.setChoice("APPROVE");
        historyService.callback(dto);
        return Result.ok(true);
    }

    /**
     * 工作流回调-审批拒绝
     * @param dto
     * @return
     */
    @ApiOperation("审批拒绝")
    @PostMapping("refused")
    public Result refused(@RequestBody WfApprovalDto dto) {
        log.info("Work flow refused callback,{}", dto);
        dto.setChoice("REFUSE");
        historyService.callback(dto);
        return Result.ok(true);
    }

    @ApiOperation("根据code获取表单数据")
    @GetMapping("getByCode")
    public Result getByCode() {
        return Result.ok(SpringUtil.getBean(FormFeignClient.class).getFormDefByCode(CeritificateAndEmpHistoryDo.code).getData());
    }

}
