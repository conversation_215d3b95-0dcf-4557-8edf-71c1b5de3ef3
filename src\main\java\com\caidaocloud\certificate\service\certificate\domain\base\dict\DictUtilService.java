package com.caidaocloud.certificate.service.certificate.domain.base.dict;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.certificate.service.certificate.domain.base.util.FunUtil;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hr.core.feign.IDictFeignClient;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SysParamDictDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class DictUtilService {
    private IDictFeignClient iDictFeignClient;

    public void setDictFeignClient(IDictFeignClient iDictFeignClient) {
        this.iDictFeignClient = iDictFeignClient;
    }

    public List<KeyValue> getEnableDictList(String typeCode, String belongModule) {
        Result result = iDictFeignClient.getEnableDictList(typeCode, belongModule);
        if (!result.isSuccess()) {
            return Lists.newArrayList();
        }
        Object data = result.getData();
        return FastjsonUtil.toArrayList(FastjsonUtil.toJson(data), KeyValue.class);
    }

    public Result getEnableDictListAllInfo(String typeCode, String belongModule) {
       return iDictFeignClient.getEnableDictList(typeCode, belongModule);
    }

    public String getLangText(String dictValue, String defaultText, String heander){
        if(StringUtil.isEmpty(dictValue)){
            return defaultText;
        }
        String dictKey = String.format("DICT_%s", dictValue);
        dictValue = SpringUtil.getBean(CacheService.class).getValue(dictKey);
        SysParamDictDto dictDto = FastjsonUtil.toObject(dictValue, SysParamDictDto.class);
        if(heander.equals("zh-CN")){
            return FunUtil.getOrDefault(dictDto.getDictChnName(), defaultText);
        }else if (heander.equals("en-US")){
            return FunUtil.getOrDefault(dictDto.getDictEngName(), defaultText);
        }else {
            Map map = FastjsonUtil.toObject((String) dictDto.getDictNameLang(), Map.class);
            return map.get("ja")==null?defaultText: String.valueOf(map.get("ja"));
        }

    }
}
