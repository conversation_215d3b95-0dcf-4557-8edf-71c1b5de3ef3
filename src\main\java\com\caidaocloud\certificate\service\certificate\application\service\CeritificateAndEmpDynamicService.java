package com.caidaocloud.certificate.service.certificate.application.service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.caidaocloud.certificate.service.certificate.application.dto.EmpInfoDto;
import com.caidaocloud.certificate.service.certificate.application.dto.EmpSearchDto;
import com.caidaocloud.certificate.service.certificate.application.feign.IHrWorkFeign;
import com.caidaocloud.certificate.service.certificate.application.feign.MasterDataEmpInfoFeign;
import com.caidaocloud.certificate.service.certificate.domain.base.util.LangUtil;
import com.caidaocloud.certificate.service.certificate.domain.base.util.TagProperty;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateProjectDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateTypeDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateUsageRecordDo;
import com.caidaocloud.certificate.service.certificate.domain.enums.BooleanValueEnum;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateSetType;
import com.caidaocloud.certificate.service.certificate.domain.enums.ValidValueEnum;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICeritificateAndEmpRepository;
import com.caidaocloud.certificate.service.certificate.infrastructure.util.DataConvertUtil;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.AvailableEmpQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateTypeQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateAndEmpExcelVo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.hrpaas.paas.common.feign.FormFeignClient;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/15 11:38
 **/
@Service
@Slf4j
public class CeritificateAndEmpDynamicService {
    @Resource
    private CertificateDo certificateDo;
    @Resource
    private ICeritificateAndEmpRepository ceritificateAndEmpRepository;
    @Autowired
    private CertificateTypeDo certificateTypeDo;
    @Resource
    private IHrWorkFeign hrWorkFeign;
    @Resource
    private CertificateSettingService certificateSettingService;

    private List<Map<String, Object>> convertTableVo(List<CeritificateAndEmpDo> items, String header) {
        if (items.isEmpty()) {
            return new ArrayList<>();
        }
        List<CertificateDo> certificateDos = certificateDo.selectList(new CertificateQueryDto());
        Map<String, CertificateDo> certificateI18nMap = certificateDos.stream()
                .collect(Collectors.toMap(AbstractData::getBid, obj -> obj, (a, b) -> a));

        List<CertificateTypeDo> certificateTypeDos = certificateTypeDo.selectList(new CertificateTypeQueryDto());
        Map<String, CertificateTypeDo> typeI18nMap = certificateTypeDos.stream()
                .collect(Collectors.toMap(AbstractData::getBid, obj -> obj, (a, b) -> a));

        EmpSearchDto dto = new EmpSearchDto();
        dto.setEmpIds(Sequences.sequence(items).map(CeritificateAndEmpDo::getEmpId).toList());
        // TODO: 2025/7/31 改为hr接口
        List<Map> empInfo = SpringUtil.getBean(IHrWorkFeign.class)
                .listEmpInfoSimple(dto.getEmpIds(), System.currentTimeMillis()).getData();
        Map<String, Map<String, Object>> empInfoMap = Sequences.sequence(empInfo).map(emp -> {
            Map<String, Object> map = new HashMap<>();
            Map<String, Object> empPrivateInfo = (Map) emp.get("empPrivateInfo");
            Map<String, Object> empWorkInfo = (Map) emp.get("empWorkInfo");
            buildEmpMap(map, empWorkInfo, "entity.hr.EmpWorkInfo");
            buildEmpMap(map, empPrivateInfo, "entity.hr.EmpPrivateInfo");
            String empId = (String) empWorkInfo.get("empId");
            map.put("empId", empId);
            return map;
        }).stream().collect(Collectors.toMap(map -> (String) map.get("empId"), obj -> obj));
        return Sequences.sequence(items).map(data -> {
            CeritificateAndEmpDto ceritificateAndEmpDto = buildCertificateVo(header, certificateI18nMap, typeI18nMap, data);
            Map<String, Object> map = empInfoMap.get(data.getEmpId());
            Map<String, Object> certificateMap = DataConvertUtil.convert2map(ceritificateAndEmpDto, CeritificateAndEmpDto.class);
            for (Map.Entry<String, Object> entry : certificateMap.entrySet()) {
                map.put("entity.certificate.certificateAndEmp@" + entry.getKey(), entry.getValue());
            }
            return map;
        }).toList();
    }

    @NotNull
    private CeritificateAndEmpDto buildCertificateVo(String header, Map<String, CertificateDo> certificateI18nMap, Map<String, CertificateTypeDo> typeI18nMap, CeritificateAndEmpDo data) {
        data.checkStatusAndUseStatus();
        CeritificateAndEmpDto vo = ObjectConverter.convert(data, CeritificateAndEmpDto.class);

        CertificateDo certificate = certificateI18nMap.get(vo.getCeritifiCateBid());
        CertificateTypeDo typeDo = typeI18nMap.get(vo.getTypeBid());
        CertificateTypeDo subTypeDo = typeI18nMap.get(vo.getTypeSubBid());
        if (certificate != null && certificate.getI18nName() != null) {
            Map certificateI18n = FastjsonUtil.toObject(certificate.getI18nName(), Map.class);
            vo.setCeritifiCateName(certificateI18n.get(header) == null ? (String) certificateI18n.get("default")
                    : (String) certificateI18n.get(header));
        }
        if (typeDo != null && typeDo.getI18nName() != null) {
            Map typeI18n = FastjsonUtil.toObject(typeDo.getI18nName(), Map.class);
            vo.setTypeName(typeI18n.get(header) == null ? (String) typeI18n.get("default")
                    : (String) typeI18n.get(header));
        }
        if (subTypeDo != null && subTypeDo.getI18nName() != null) {
            Map subTypeI18n = FastjsonUtil.toObject(subTypeDo.getI18nName(), Map.class);
            vo.setTypeSubName(subTypeI18n.get(header) == null ? (String) subTypeI18n.get("default")
                    : (String) subTypeI18n.get(header));
        }
        if (data.getI18nName() != null)
            vo.setSpecialty(LangUtil.getCurrentLangVal(FastjsonUtil.toObject(data.getI18nName(), Map.class)));

        vo.getIsRegister().setText(BooleanValueEnum.getLanguageTxt(vo.getIsRegister().getText(), header));
        vo.getIsuse().setText(BooleanValueEnum.getLanguageTxt(vo.getIsuse().getText(), header));
        vo.getStatus().setText(ValidValueEnum.getLanguageTxt(vo.getStatus().getText(), header));
        vo.getUseStatus().setText(ValidValueEnum.getUseLanguageTxt(vo.getUseStatus().getText(), header));
        return vo;
    }

    private void buildEmpMap(Map<String, Object> map, Map<String, Object> empWorkInfo, String identifier) {
        for (Map.Entry<String, Object> entry : empWorkInfo.entrySet()) {
            String key = identifier + "@" + entry.getKey();
            if ("ext".equals(key)) {
                Map<String,Object> ext = (Map) entry.getValue();
                for (Map.Entry<String, Object> e : ext.entrySet()) {
                    map.put(key + "@" + e.getKey(), e.getValue());
                }
            }else{
                map.put(key, entry.getValue());
            }
        }
    }

    public List<CertificateAndEmpExcelVo> selectExportRecordPage(CeritificateAndEmpQueryDto dto) {
        dto.setPageSize(-1);
        dto.setPageNo(1);
        List<Map> doList = tablePage(dto).getItems();
        List<CertificateAndEmpExcelVo> list = BeanUtil.convertList(doList, CertificateAndEmpExcelVo.class);
        list.forEach(t -> {
            t.setStatustxt(t.getStatus().getText());
            t.setCertificateStatus(t.getUseStatus().getText());
            t.setIsRegisterTxt(t.getIsRegister().getText());
            t.setIssueAtTxt(t.getIssueAt().getText());
            t.setIsuseTxt(t.getIsuse().getText());
            t.setAccessProvinceTxt(t.getAccessProvince().getText());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            if (t.getRegisterTime() != null) {
                Date registerTime = new Date(Long.valueOf(t.getRegisterTime()));
                t.setRegisterTime(sdf.format(registerTime));
            }
            if (t.getExpireTime() != null) {
                Date expireTime = new Date(Long.valueOf(t.getExpireTime()));
                t.setExpireTime(sdf.format(expireTime));
            }
            if (t.getAcquiredTime() != null) {
                Date date = new Date(t.getAcquiredTime());
                t.setAcquiredTimeTxt(sdf.format(date));
            }
        });
        return list;
    }


    public PageResult<Map> tablePage(CeritificateAndEmpQueryDto vo) {
        HttpServletRequest request = WebUtil.getRequest();
        String header = request.getHeader("Accept-Language");

        if (StringUtils.isNotEmpty(vo.getApiType())) {
            if (vo.getApiType().equals("2")) {
                // vo.setUseStatus("0");
                vo.setCeritificateStatus("0");
                vo.setEmpStatus("0");
            }
        }
        // if (StringUtils.isEmpty(vo.getEmpStatus())) {
        // vo.setEmpStatus("0");
        // }
        PageResult<CeritificateAndEmpDo> page = ceritificateAndEmpRepository.queryTablePage(vo, vo.getPageSize(),
                vo.getPageNo());
        return new PageResult(convertTableVo(page.getItems(), header), vo.getPageNo(), vo.getPageSize(),
                page.getTotal());
    }

    public PageResult<Map> loadAvailableEmployees(AvailableEmpQueryDto queryDto) {
        List<String> rangeCertificate = new ArrayList<>();
        if (queryDto.getStartTime() != null && queryDto.getEndTime() != null) {
            List<CertificateUsageRecordDo> recordDoList = CertificateProjectDo
                    .listRecordByRange(queryDto.getCeritifiCateBid(), queryDto.getStartTime(), queryDto.getEndTime());
            rangeCertificate = Sequences.sequence(recordDoList).map(CertificateUsageRecordDo::getEmpCertificate)
                    .toList();
            queryDto.setExcludeBid(rangeCertificate);
        }
        queryDto.setCeritificateStatus(null);
        return tablePage(queryDto);
    }

    public  List<TagProperty> dynamicColumn() {
        certificateSettingService.selectList(CertificateSetType.CERTIFICATE);
        return new TagProperty[0];
    }
}
