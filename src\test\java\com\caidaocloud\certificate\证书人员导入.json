{"config": [{"fileImportAppend": false, "identifier": "entity.certificate.certificateAndEmp", "persistType": "paas", "properties": [{"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "员工工号", "property": "empId", "required": true, "type": "WORK_NO_TO_EMP_ID", "writeProperty": "empId"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "证书类型名称", "property": "typeName", "required": true, "type": "COMMON", "writeProperty": "typeName"}, {"property": "typeBid", "previous": "typeName", "columnOmittedEnabled": true, "required": false, "existTarget": "entity|entity.certificate.certificateType", "existProperty": "name", "returnProperty": "bid", "type": "EXIST", "writeProperty": "typeBid"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "证书子类型名称", "property": "typeSubName", "required": true, "type": "COMMON", "writeProperty": "typeSubName"}, {"property": "typeSubBid", "previous": "typeSubName", "columnOmittedEnabled": true, "required": false, "existTarget": "entity|entity.certificate.certificateType", "existProperty": "name", "returnProperty": "bid", "type": "EXIST", "writeProperty": "typeSubBid"}, {"columnOmittedEnabled": true, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "证书名称", "property": "ceritifiCateName", "required": true, "type": "COMMON", "writeProperty": "ceritifiCateName"}, {"property": "ceritifiCateBid", "previous": "ceritifiCateName", "columnOmittedEnabled": true, "required": false, "existTarget": "entity|entity.certificate.certificate", "existProperty": "name", "returnProperty": "bid", "type": "EXIST", "writeProperty": "ceritifiCateBid"}, {"columnOmittedEnabled": true, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "证书编号", "property": "ceritifiCateCode", "required": false, "type": "COMMON", "writeProperty": "ceritifiCateCode"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "注册日期", "format": "yyyy/MM/dd", "property": "registerTime", "required": true, "type": "DATE", "writeProperty": "registerTime"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "失效日期", "format": "yyyy/MM/dd", "property": "expireTime", "required": true, "type": "DATE", "writeProperty": "expireTime"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "注册号", "property": "registrationNo", "required": false, "type": "COMMON", "writeProperty": "registrationNo"}, {"columnOmittedEnabled": true, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "专业", "property": "specialty", "required": false, "type": "COMMON", "writeProperty": "specialty"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "dictType": "IssueAt", "enumMap": {}, "excelColumn": "签发地", "property": "issueAt", "required": false, "type": "DICT", "writeProperty": "issueAt"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "dictType": "AccessProvince", "enumMap": {}, "excelColumn": "入省备案", "property": "accessProvince", "required": false, "type": "DICT", "writeProperty": "accessProvince"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {"否": "1", "是": "0"}, "excelColumn": "是否限制使用", "property": "isuse", "required": true, "type": "ENUM", "writeProperty": "isuse"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {"否": "1", "是": "0"}, "excelColumn": "是否注册", "property": "isRegister", "required": true, "type": "ENUM", "writeProperty": "isRegister"}], "submitAlone": false, "update": {"datas": [{"ignoreWhenUpdate": [], "opr": "eq", "property": "empId"}, {"ignoreWhenUpdate": [], "opr": "eq", "property": "typeName"}, {"ignoreWhenUpdate": [], "opr": "eq", "property": "typeSubName"}, {"ignoreWhenUpdate": [], "opr": "eq", "property": "ceritifiCateName"}, {"ignoreWhenUpdate": [], "opr": "eq", "property": "registerTime"}], "ignoreWhenUpdate": [], "opr": "and"}}], "description": "1、按工号+证书类别+证书子类+证书+注册日期的唯一性，存在则更新；不存在则新增"}