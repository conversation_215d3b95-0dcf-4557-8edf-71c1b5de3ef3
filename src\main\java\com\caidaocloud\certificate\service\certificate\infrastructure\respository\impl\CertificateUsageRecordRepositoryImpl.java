package com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl;

import java.util.List;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateUsageRecordDo;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateStatus;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateUsageRecordRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordQueryDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Repository;

@Repository
public class CertificateUsageRecordRepositoryImpl extends BaseRepositoryImpl<CertificateUsageRecordDo> implements ICertificateUsageRecordRepository {


	@Override
	public CertificateUsageRecordDo save(CertificateUsageRecordDo record) {
		if (record.getBid() == null) {
			record.setBid(SnowUtil.nextId());
			DataInsert.identifier(record.getIdentifier()).insert(record);
		}
		else {
			DataUpdate.identifier(record.getIdentifier()).update(record);
		}
		return record;

	}

	@Override
	public PageResult<CertificateUsageRecordDo> selectPage(CertificateUsageRecordQueryDto dto) {
		return DataQuery.identifier(CertificateUsageRecordDo.IDENTIFIER).specifyLanguage().queryInvisible().decrypt()
				.limit(dto.getPageSize(), dto.getPageNo())
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
						.andEq("projectId", dto.getProjectId()), CertificateUsageRecordDo.class);
	}

	@Override
	public PageResult<CertificateUsageRecordDo> selectUsageRecordByCertificate(String bid, String empCertificate, CertificateStatus status) {
		DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
				.andNeIf("bid", bid, () -> bid != null)
				.andEq("empCertificate", empCertificate);
		if (status != null) {
			filter=filter.andEq("certificateStatus", status.name());
		}
		return DataQuery.identifier(CertificateUsageRecordDo.IDENTIFIER).specifyLanguage().queryInvisible().decrypt()
				.limit(-1, 1)
				.filter(filter, CertificateUsageRecordDo.class);
	}

	@Override
	public List<CertificateUsageRecordDo> selectUsageRecordByTime(long currentTimestamp) {
		return selectUsageRecordPageByTime(currentTimestamp, -1, 1).getItems();
	}

	@Override
	public PageResult<CertificateUsageRecordDo> selectUsageRecordPageByTime(long currentTimestamp, int pageSize, int pageNo) {
		DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
				.and(DataFilter.eq("startDate", String.valueOf(currentTimestamp))
						.orEq("endDate", String.valueOf(currentTimestamp)));
		return DataQuery.identifier(CertificateUsageRecordDo.IDENTIFIER).specifyLanguage().queryInvisible().decrypt()
				.limit(pageSize, pageNo)
				.filter(filter, CertificateUsageRecordDo.class);
	}

	@Override
	public List<CertificateUsageRecordDo> listByRange(String ceritifiCateBid, Long startTime, Long endTime, String empCertificate) {
		return DataQuery.identifier(CertificateUsageRecordDo.IDENTIFIER).specifyLanguage().queryInvisible().decrypt()
				.limit(-1, 1)
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString())
						.andEqIf("certificate", ceritifiCateBid, () -> StringUtils.isNotEmpty(ceritifiCateBid))
						.andEqIf("empCertificate", empCertificate, () -> StringUtils.isNotEmpty(empCertificate))
						.andLe("startDate", String.valueOf(endTime))
						.andGe("endDate", String.valueOf(startTime)), CertificateUsageRecordDo.class).getItems();

	}

}
