### 测试证书类型和证书树形结构接口
POST http://localhost:8080/certificateType/getTreeData
Content-Type: application/json
Accept-Language: zh-CN

{
  "status": "0"
}

### 测试证书类型和证书树形结构接口 - 英文
POST http://localhost:8080/certificateType/getTreeData
Content-Type: application/json
Accept-Language: en-US

{
  "status": "0"
}

### 测试证书分页查询 - 不指定typeBid（使用join查询）
POST http://localhost:8080/certificate/pageList
Content-Type: application/json
Accept-Language: zh-CN

{
  "pageNo": 1,
  "pageSize": 10,
  "status": "0"
}

### 测试证书分页查询 - 指定typeBid（使用原有查询）
POST http://localhost:8080/certificate/pageList
Content-Type: application/json
Accept-Language: zh-CN

{
  "pageNo": 1,
  "pageSize": 10,
  "status": "0",
  "typeBid": "specific-type-bid"
}

### 测试证书分页查询 - 带名称或编码搜索（使用join查询）
POST http://localhost:8080/certificate/pageList
Content-Type: application/json
Accept-Language: zh-CN

{
  "pageNo": 1,
  "pageSize": 10,
  "status": "0",
  "nameOrCode": "测试"
}

### 测试查询拥有指定证书且可使用的员工 - 不指定时间范围（分页查询）
POST http://localhost:8080/api/certificate/v1/project/available-employees
Content-Type: application/json
Accept-Language: zh-CN

{
  "certificateId": "certificate-bid-123",
  "pageNo": 1,
  "pageSize": 10
}

### 测试查询拥有指定证书且可使用的员工 - 指定时间范围（分页查询）
POST http://localhost:8080/api/certificate/v1/project/available-employees
Content-Type: application/json
Accept-Language: zh-CN

{
  "certificateId": "certificate-bid-123",
  "startTime": 1703001600000,
  "endTime": 1703088000000,
  "pageNo": 1,
  "pageSize": 10
}

### 测试查询拥有指定证书且可使用的员工 - 第二页
POST http://localhost:8080/api/certificate/v1/project/available-employees
Content-Type: application/json
Accept-Language: zh-CN

{
  "certificateId": "certificate-bid-123",
  "pageNo": 2,
  "pageSize": 5
}

### 测试查询拥有指定证书且可使用的员工 - 参数验证（缺少证书ID）
POST http://localhost:8080/api/certificate/v1/project/available-employees
Content-Type: application/json
Accept-Language: zh-CN

{
  "pageNo": 1,
  "pageSize": 10,
  "startTime": 1703001600000,
  "endTime": 1703088000000
}

### 测试查询拥有指定证书且可使用的员工 - 参数验证（时间范围错误）
POST http://localhost:8080/api/certificate/v1/project/available-employees
Content-Type: application/json
Accept-Language: zh-CN

{
  "certificateId": "certificate-bid-123",
  "pageNo": 1,
  "pageSize": 10,
  "startTime": 1703088000000,
  "endTime": 1703001600000
}

### 测试查询拥有指定证书且可使用的员工 - 参数验证（分页大小超限）
POST http://localhost:8080/api/certificate/v1/project/available-employees
Content-Type: application/json
Accept-Language: zh-CN

{
  "certificateId": "certificate-bid-123",
  "pageNo": 1,
  "pageSize": 1001
}
