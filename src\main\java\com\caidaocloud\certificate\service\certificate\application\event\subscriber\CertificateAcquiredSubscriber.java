package com.caidaocloud.certificate.service.certificate.application.event.subscriber;

import java.util.Optional;

import com.caidaocloud.certificate.service.certificate.application.event.CertificateAcquiredEvent;
import com.caidaocloud.certificate.service.certificate.application.service.CeritificateAndEmpService;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateUsageRecordDo;
import com.caidaocloud.msg.handler.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/5/15
 */
@Component
@Slf4j
public class CertificateAcquiredSubscriber  implements MessageHandler<CertificateAcquiredEvent> {
	@Autowired
	private CeritificateAndEmpService ceritificateAndEmpService;

	@Override
	public String topic() {
		return CertificateAcquiredEvent.TOPIC;
	}

	@Override
	public void handle(CertificateAcquiredEvent message) throws Exception {
		log.info("CertificateAcquiredEvent handle,msg={}", message);
		try {
			SecurityUserInfo userInfo = new SecurityUserInfo();
			userInfo.setTenantId(message.getTenantId());
			userInfo.setUserId(0L);
			userInfo.setEmpId(0L);
			SecurityUserUtil.setSecurityUserInfo(userInfo);

			Optional<CertificateUsageRecordDo> locked = CertificateUsageRecordDo.findLockedByEmpCertificate(message.getEmpCertificate());
			if (locked.isPresent()) {
				ceritificateAndEmpService.projectAcquired(locked.get().getEmpCertificate(), locked.get()
						.getProjectId());
			}
			else {
				ceritificateAndEmpService.projectReset(message.getEmpCertificate());
			}
		}
		finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
		log.info("CertificateAcquiredEvent handle end");
	}
}
