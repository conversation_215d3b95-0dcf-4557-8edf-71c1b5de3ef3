package com.caidaocloud.certificate.service.certificate.domain.enums;

public enum BooleanValueEnum {
    NO( "否","no","いいえ"),
    YES("是","yes","はい");

    public String zn;
    public String en;
    public String ja;

    BooleanValueEnum(String zn, String en,String ja) {
        this.zn=zn;
        this.en=en;
        this.ja=ja;
    }

    public static String  getLanguageTxt(String enumValue,String header) {
       String value= enumValue.equals("是")?"YES":enumValue==null?"":"NO";
        if (header.contains("zh-CN")) {
            return BooleanValueEnum.valueOf(value).zn;
        } else if (header.contains("en-US")) {
            return BooleanValueEnum.valueOf(value).en;
        } else {
            return BooleanValueEnum.valueOf(value).ja;
        }
    }

}
