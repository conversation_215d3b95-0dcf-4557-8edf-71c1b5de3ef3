package com.caidaocloud.certificate.service.infrastructure.config.workflow;

import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpHistoryDo;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefMetadataDto;
import com.caidaocloud.hrpaas.paas.common.feign.FormFeignClient;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.configuration.WfFunctionConfiguration;
import com.caidaocloud.workflow.dto.WfMetaCallbackDto;
import com.caidaocloud.workflow.dto.WfMetaFunDto;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.enums.WfCallbackTimeTypeEnum;
import com.caidaocloud.workflow.enums.WfCallbackTypeEnum;
import com.caidaocloud.workflow.enums.WfFieldDataTypeEnum;
import com.caidaocloud.workflow.enums.WfFunctionPageJumpType;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.caidaocloud.workflow.service.IBusinessDetailService;
import com.google.common.collect.ImmutableMap;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作流-流程应用功能注册
 *
 */
@Slf4j
@Component
public class WfFunctionConfig extends IBusinessDetailService {
    @Value("${spring.application.name:}")
    private String appName;
    @Resource
    private IWfRegisterFeign iWfRegisterFeign;
    @Resource
    private FormFeignClient formFeignClient;

    private final String dissolveFunCode = "CERTIFICATE-EMP";
    public static final List<WfMetaFunFormFieldDto> dissolveFormFields = Lists.list(
            new WfMetaFunFormFieldDto("typeName", "证书类型", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("typeSubName", "证书子类型", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("ceritifiCateName", "证书名称", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("ceritifiCateCode", "证书编码", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("expireTime", "失效日期", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("registerTime", "注册日期", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("registrationNo", "注册号", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("i18nName", "专业国际化", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("issueAt", "签发地", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("accessProvince", "入省备案", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("isuse", "是否限制使用", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("isRegister", "是否注册", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("remake", "备注", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("annex", "附件", WfFieldDataTypeEnum.Text)
    );
    @PostConstruct
    public void initWfRegister() {
        try {
            List<WfMetaFunFormFieldDto> wfMetaFunFormFieldDtos = doInitWfRegister();
            WfFunctionConfiguration.getFunFieldMap().putIfAbsent(dissolveFunCode, ImmutableMap.copyOf(wfMetaFunFormFieldDtos.stream()
                    .collect(Collectors.toMap(e -> e.getCode(), e -> e, (v1, v2) -> v2))));
        } catch (Exception e) {
            log.error("registerFunction Exception,{}", e);
        }
    }

    private  List<WfMetaFunFormFieldDto> doInitWfRegister() {
        List<WfMetaFunFormFieldDto> formFields = Lists.list();
        formFields.add(new WfMetaFunFormFieldDto("typeName", "证书类型", WfFieldDataTypeEnum.Text));
        formFields.add(new WfMetaFunFormFieldDto("typeSubName", "证书子类型", WfFieldDataTypeEnum.Text));
        formFields.add(new WfMetaFunFormFieldDto("ceritifiCateName", "证书名称", WfFieldDataTypeEnum.Text));
        formFields.add(new WfMetaFunFormFieldDto("ceritifiCateCode", "证书编码", WfFieldDataTypeEnum.Text));
        formFields.add(new WfMetaFunFormFieldDto("expireTime", "失效日期", WfFieldDataTypeEnum.Text));
        formFields.add(new WfMetaFunFormFieldDto("registerTime", "注册日期", WfFieldDataTypeEnum.Text));
        formFields.add(new WfMetaFunFormFieldDto("registrationNo", "注册号", WfFieldDataTypeEnum.Text));
        formFields.add(new WfMetaFunFormFieldDto("specialty", "专业", WfFieldDataTypeEnum.Text));
        formFields.add(new WfMetaFunFormFieldDto("issueAt", "签发地", WfFieldDataTypeEnum.Text));
        formFields.add(new WfMetaFunFormFieldDto("isuse", "是否限制使用", WfFieldDataTypeEnum.Text));
        formFields.add(new WfMetaFunFormFieldDto("isRegister", "是否注册", WfFieldDataTypeEnum.Text));
        formFields.add(new WfMetaFunFormFieldDto("remake", "备注", WfFieldDataTypeEnum.Text));
        formFields.add(new WfMetaFunFormFieldDto("annex", "附件", WfFieldDataTypeEnum.Text));

        Result<FormDefDto> data = formFeignClient.getFormDefByCode(CeritificateAndEmpHistoryDo.code);
        if (ObjectUtil.isEmpty(data) && ObjectUtil.isEmpty(data.getData())) {
            log.info("Initialize form data");
            List<FormDefMetadataDto> properties = data.getData().getProperties();
            properties.stream().forEach(t->{
                formFields.add(new WfMetaFunFormFieldDto(t.getProperty(), t.getName(),  WfFieldDataTypeEnum.Text));
            });

        }
        //员工证书申请
        WfMetaFunDto dto = new WfMetaFunDto("员工证书申请", dissolveFunCode,
                WfFunctionPageJumpType.RELATIVE_PATH, "",
                "caidaocloud-certificate-service",
                "", "/api/certificate/v1/history/get", "", formFields);
        iWfRegisterFeign.registerFunction(dto);

        registerCallback(dissolveFunCode, "回调失败", "CERTIFICATE-FAIL", "/api/certificate/v1/history/fail");
        WfMetaCallbackDto wmc;

        registerCallback(dissolveFunCode, "回调成功", "CERTIFICATE-SUCCESS", "/api/certificate/v1/history/success");
        WfMetaCallbackDto wmcc;

        return formFields;

    }



    private void registerCallback(String newSignFunCode, String name, String code, String apiPath) {
        WfMetaCallbackDto wmc = new WfMetaCallbackDto(name, code, Lists.list(newSignFunCode),
                "",
                apiPath,
                "caidaocloud-certificate-service",
                "",
                WfCallbackTypeEnum.RELATIVE_PATH,
                WfCallbackTimeTypeEnum.NOW);
        iWfRegisterFeign.registerCallback(wmc);
    }

    @Override
    protected Map<String, Object> getBusinessDetail(String businessKey) {
        return null;
    }

    @Override
    protected List<String> belongFunCode() {
        return null;
    }
}