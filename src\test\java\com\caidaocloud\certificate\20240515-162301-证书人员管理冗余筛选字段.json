[{"action": "add_prop", "identifier": "entity.certificate.certificateAndEmp", "propertyDef": [{"property": "empStatus", "name": "员工状态", "dataType": "Enum", "widgetType": "enum", "visible": true, "modifiable": false, "defaultValue": {"value": 0, "type": "Enum"}, "enumDef": [{"value": 0, "display": "在职"}, {"value": 1, "display": "离职"}]}, {"property": "post", "name": "岗位", "dataType": "String", "widgetType": "text"}, {"property": "job", "name": "职务", "dataType": "String", "widgetType": "text"}, {"property": "jobLevel", "name": "职级", "dataType": "String", "widgetType": "text"}, {"property": "post", "name": "持证数量", "dataType": "Integer", "widgetType": "int"}, {"property": "workAge", "name": "工龄", "dataType": "Integer", "widgetType": "int"}, {"property": "socialSecurity", "name": "社保缴纳地", "dataType": "Address", "widgetType": "AddressCascader"}, {"property": "certificateStatus", "name": "证书状态", "dataType": "Enum", "widgetType": "enum", "visible": true, "modifiable": false, "defaultValue": {"value": 0, "type": "Enum"}, "enumDef": [{"value": 0, "display": "可使用"}, {"value": 1, "display": "使用中"}]}, {"property": "status", "name": "状态", "dataType": "Enum", "widgetType": "enum", "visible": true, "modifiable": false, "defaultValue": {"value": 0, "type": "Enum"}, "enumDef": [{"value": 0, "display": "有效"}, {"value": 1, "display": "无效"}]}]}]