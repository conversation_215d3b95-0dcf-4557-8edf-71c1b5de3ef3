package com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.domain.repository.IEmpInfoRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.MdDataQuery;
import com.caidaocloud.masterdata.entity.emp.EmpInfoEntity;
import com.caidaocloud.util.StringUtil;
import org.springframework.stereotype.Repository;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/15 10:43
 **/
@Repository
public class EmpInfoRepositoryImpl extends BaseRepositoryImpl<EmpInfoEntity> implements IEmpInfoRepository {
    @Override
    public List<EmpInfoEntity> queryListByEmpIds(List<String> empIds) {
        return MdDataQuery.identifier(EmpInfoEntity.EMP_IDENTIFIER).queryInvisible().decrypt().specifyLanguage()
                .limit(Math.max(empIds.size(),5000), 1)
                .filter(DataFilter.in("empId", empIds), EmpInfoEntity.class, System.currentTimeMillis())
                .getItems();
    }

    @Override
    public List<EmpInfoEntity> queryList(CeritificateAndEmpQueryDto data, long currentTimeMillis) {
        DataFilter filter = DataFilter.eq("tenantId", UserContext.getTenantId()).andNe("deleted", Boolean.TRUE.toString());
        //公司名称/编码 单信息检索；
        if (StringUtil.isNotEmpty(data.getNameOrNo())) {
            filter = filter.and(DataFilter.regex("workno", data.getNameOrNo()).or(DataFilter.regex("name", data.getNameOrNo())));
        }

        //员工状态
        if (StringUtil.isNotEmpty(data.getEmpStatus())) {
            filter = filter.and(DataFilter.eq("empStatus", data.getEmpStatus()));
        }
        //员工状态
        if (StringUtil.isNotEmpty(data.getEmpId())) {
            filter = filter.and(DataFilter.eq("empId", data.getEmpId()));
        }

        //任职职级
        if (StringUtil.isNotEmpty(data.getJobLevel())) {
            filter = filter.and(DataFilter.eq("jobGrade$startLevel", data.getJobLevel()));
        }
        //任职工龄比较
        if (StringUtil.isNotEmpty(data.getWorkAge())) {
            Long time=ToformatTime(data.getWorkAge());
            filter = filter.and(DataFilter.le("hireDate", time.toString()));
        }


        return DataQuery.identifier(EmpInfoEntity.EMP_IDENTIFIER).decrypt().specifyLanguage().queryInvisible()
                .limit(data.getPageSize(), data.getPageNo()).filter(filter, EmpInfoEntity.class).getItems();
    }

    private Long ToformatTime(Double workAge) {

        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        int year=(int)Math.floor(workAge);

        // 将当前日期加上4.5年
        calendar.add(Calendar.YEAR, -year);
        Date futureDate = calendar.getTime();

        // 将日期转换为时间戳
        long futureTimestamp = futureDate.getTime();
        return futureTimestamp;
    }


}
