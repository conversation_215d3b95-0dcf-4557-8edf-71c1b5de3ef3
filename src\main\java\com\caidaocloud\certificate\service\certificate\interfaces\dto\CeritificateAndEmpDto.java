package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/15 13:35
 *  证书人员管理dto
 **/
@Data
public class CeritificateAndEmpDto extends BasePage {
    @ApiModelProperty("bid")
    private String bid;
    @ApiModelProperty("员工id")
    private String empId;
    @ApiModelProperty("员工姓名")
    private String workName;
    @ApiModelProperty("员工工号")
    private String workNo;
    @ApiModelProperty("证书类型")
    private String typeName;
    @ApiModelProperty("证书类型bid")
    private String typeBid;
    @ApiModelProperty("证书子类型")
    private String typeSubName;
    @ApiModelProperty("证书子类型bid")
    private String typeSubBid;
    @ApiModelProperty("证书名称")
    private String ceritifiCateName;
    @ApiModelProperty("证书编码")
    private String ceritifiCateCode;
    @ApiModelProperty("证书id")
    private String ceritifiCateBid;
    @ApiModelProperty("取得日期")
    private Long acquiredTime;
    @ApiModelProperty("注册日期")
    private String registerTime;
    @ApiModelProperty("失效日期")
    private String expireTime;
    @ApiModelProperty("注册号")
    private String registrationNo;
    @ApiModelProperty("专业")
    private String specialty;
    @ApiModelProperty("专业多语言字段")
    private Map<String,Object> i18nName;
    @ApiModelProperty("签发地")
    private DictSimple issueAt;
    private String issueAuthority;
    @ApiModelProperty("入省备案")
    private DictSimple accessProvince;
    @ApiModelProperty("是否限制使用 0是1否")
    private EnumSimple isuse;
    @ApiModelProperty("是否注册  0是1否")
    private EnumSimple isRegister;
    @ApiModelProperty("备注")
    private String remake;
    @ApiModelProperty("附件")
    private Attachment attachFile;
    @ApiModelProperty("项目bid")
    private String proBid;
    @ApiModelProperty("组织bid")
    private String organizeId;
    @ApiModelProperty("组织")
    private String organize;
    @ApiModelProperty("岗位")
    private String post;
    @ApiModelProperty("职务")
    private String job;
    @ApiModelProperty("职级")
    private String jobLevel;
    @ApiModelProperty("岗位")
    private String postId;
    @ApiModelProperty("职务")
    private String jobId;
    @ApiModelProperty("职级")
    private String jobLevelId;
    @ApiModelProperty("持有证书数量")
    private Integer CertificateNums;
    @ApiModelProperty("社保缴纳地")
    private List<String> socialSecurity;
    @ApiModelProperty("工龄")
    private Double workAge;
    @ApiModelProperty("持证数量")
    private Integer certifiCateNums;
    /**
     *
     *证书有效状态
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private EnumSimple approveStatus;

    @ApiModelProperty("状态")
    private EnumSimple status;
    @ApiModelProperty("证书使用状态")
    private EnumSimple useStatus;
    @ApiModelProperty("员工状态")
    private EnumSimple EmpStatus;
    @ApiModelProperty("表单id")
    private String formId;
    @ApiModelProperty("表单数据id")
    private String formData;
    @ApiModelProperty("表单数据")
    private Map<String,Object> formDataMap;


    @ApiModelProperty("创建时间")
    private long createTime;
//*************权限范围冗余字段**************
    /**
     * 工作地
     */
    private String workPlace;
    /**
     * 公司
     */
    private String company;
    /**
     * 合同类型
     */
    private DictSimple contractSettingType;
    /**
     * 员工类型
     */
    private DictSimple empType;


}
