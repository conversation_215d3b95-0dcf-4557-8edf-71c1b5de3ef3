# 证书状态设置方法重构总结

## 重构目标
将CeritificateAndEmpService中两个相同逻辑的statusSet方法重构，将statusSet作为CeritificateAndEmpDo的方法收口，并将方法中的常量枚举重构成枚举类。

## 重构内容

### 1. 创建枚举类

#### CertificateStatusEnum (证书状态枚举)
- **位置**: `src/main/java/com/caidaocloud/certificate/service/certificate/domain/enums/CertificateStatusEnum.java`
- **枚举值**:
  - `VALID("0", "有效")` - 有效状态
  - `INVALID("1", "无效")` - 无效状态
- **方法**:
  - `toEnumSimple()` - 创建EnumSimple对象
  - `fromValue(String value)` - 根据值获取枚举

#### CertificateUseStatusEnum (证书使用状态枚举)
- **位置**: `src/main/java/com/caidaocloud/certificate/service/certificate/domain/enums/CertificateUseStatusEnum.java`
- **枚举值**:
  - `AVAILABLE("0", "可使用")` - 可使用状态
  - `IN_USE("1", "使用中")` - 使用中状态
- **方法**:
  - `toEnumSimple()` - 创建EnumSimple对象
  - `fromValue(String value)` - 根据值获取枚举

### 2. 重构CeritificateAndEmpDo类

#### 新增方法
- **setStatusAndUseStatus()**: 统一的状态设置方法，替代Service中的重复逻辑
  - 设置证书状态（status和approveStatus）
  - 设置证书使用状态（useStatus）
  - 使用枚举类替代硬编码常量

- **setStatusForDto(Object dto)**: 静态方法，用于处理DTO对象的状态设置
  - 使用反射处理CeritificateAndEmpDto对象
  - 提供统一的状态设置逻辑

#### 更新方法
- **checkActive(long time)**: 使用枚举类替代硬编码常量

### 3. 重构CeritificateAndEmpService类

#### 删除的方法
- `statusSet(CeritificateAndEmpDo t)` - 原始的状态设置方法（第一个）
- `statusSets(CeritificateAndEmpDto t)` - 原始的状态设置方法（第二个，具体实现部分）

#### 更新的方法
- `initUseStatus(CeritificateAndEmpDo dto)`: 调用`dto.setStatusAndUseStatus()`
- `statusSets(List<CeritificateAndEmpDto> list)`: 调用`CeritificateAndEmpDo.setStatusForDto()`
- `statusSets(CeritificateAndEmpDto t)`: 调用`CeritificateAndEmpDo.setStatusForDto(t)`

### 4. 创建测试类

#### CeritificateAndEmpDoTest
- **位置**: `src/test/java/com/caidaocloud/certificate/service/certificate/domain/entity/CeritificateAndEmpDoTest.java`
- **测试内容**:
  - 有效证书的状态设置
  - 过期证书的状态设置
  - 使用中证书的状态设置
  - DTO对象的状态设置
  - 枚举类的功能验证

## 重构优势

### 1. 代码复用
- 消除了Service类中的重复代码
- 将状态设置逻辑统一收口到实体类中

### 2. 类型安全
- 使用枚举类替代硬编码字符串常量
- 减少了魔法数字和字符串的使用

### 3. 可维护性
- 状态相关的逻辑集中在一个地方
- 枚举类提供了清晰的状态定义

### 4. 可扩展性
- 新增状态只需要在枚举类中添加
- 状态设置逻辑的修改只需要在一个地方进行

### 5. 测试覆盖
- 提供了完整的单元测试
- 确保重构后的功能正确性

## 使用方式

### 对于CeritificateAndEmpDo对象
```java
CeritificateAndEmpDo certificate = new CeritificateAndEmpDo();
certificate.setExpireTime("1234567890000");
certificate.setProBid("project123");
certificate.setStatusAndUseStatus(); // 统一设置状态
```

### 对于CeritificateAndEmpDto对象
```java
CeritificateAndEmpDto dto = new CeritificateAndEmpDto();
dto.setExpireTime("1234567890000");
dto.setProBid(null);
CeritificateAndEmpDo.setStatusForDto(dto); // 静态方法设置状态
```

### 使用枚举类
```java
// 创建有效状态
EnumSimple validStatus = CertificateStatusEnum.VALID.toEnumSimple();

// 根据值获取枚举
CertificateStatusEnum status = CertificateStatusEnum.fromValue("0");
```

## 注意事项

1. 重构后的代码保持了与原有逻辑的完全兼容性
2. 所有原有的调用点都已经更新为使用新的方法
3. 枚举类提供了默认值处理，确保系统的健壮性
4. 测试类验证了重构后的功能正确性

## 后续建议

1. 可以考虑将其他类似的硬编码常量也重构为枚举类
2. 可以在枚举类中添加更多的业务方法，如状态转换验证等
3. 可以考虑使用策略模式进一步优化状态处理逻辑
