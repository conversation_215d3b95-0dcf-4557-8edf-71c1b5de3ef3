package com.caidaocloud.certificate.service.certificate.interfaces.vo.org;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/11
 */
@Data
public class CustomOrgRoleVo {

    @ApiModelProperty("角色ID")
    private String role;

    @ApiModelProperty("责任人组织id")
    private String empOrg;

    @ApiModelProperty("责任人组织id")
    private String empOrgTxt;

    @ApiModelProperty("岗位组织id")
    private String postOrg;

    @ApiModelProperty("岗位组织名称")
    private String postOrgTxt;

    @ApiModelProperty("责任人")
    private EmpSimple leaderEmp;

    @ApiModelProperty("责任人（岗位）id")
    private String leaderPost;

    @ApiModelProperty("责任人（岗位）名称")
    private String leaderPostTxt;
}
