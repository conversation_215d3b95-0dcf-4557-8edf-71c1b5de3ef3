package com.caidaocloud.certificate.service.certificate.interfaces.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/12 10:33
 **/
@Data
public class CertificateExcelVo  {
    @Excel(name = "证书类型代码")
    private String typeCode;
    @Excel(name = "证书类型名称")
    private String typeName;
    @Excel(name = "证书子类型名称")
    private String typeSubName;
    @Excel(name = "证书子类型代码")
    private String typeSubCode;
    @Excel(name = "证书名称")
    private String name;
    @Excel(name = "证书代码")
    private String code;
    @Excel(name = "证书id")
    private String bid;
    @Excel(name="状态")
    private String status;
}
