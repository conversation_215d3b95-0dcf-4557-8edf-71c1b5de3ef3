package com.caidaocloud.certificate.service.certificate.infrastructure.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Workbook;

/**
 *
 * <AUTHOR>
 * @date 2024/2/28
 */
public class ExcelUtil {
	public static void downloadExcel(HttpServletResponse response, OutputStream out, Workbook wb,String fileName) throws IOException {
		response.setCharacterEncoding("UTF-8");
		response.setHeader("content-Type", "application/vnd.ms-excel");
		response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xls", "UTF-8"));
		try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
			wb.write(baos);
			response.setHeader("Content-Length", String.valueOf(baos.size()));
			out.write(baos.toByteArray());
		}
	}
}