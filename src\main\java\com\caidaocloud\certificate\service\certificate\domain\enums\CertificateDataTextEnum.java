package com.caidaocloud.certificate.service.certificate.domain.enums;

/**
 * 门户详情文本多语言枚举类型
 */
public enum CertificateDataTextEnum {
    workNo("员工工号", "Employee Number", "社員番号"),
    workName("员工姓名", "Employee Name", "社員名"),
    applyName("申请人", "Applicant", "申請者"),
    launchName("发起人", "Launcher", "開始者"),
    typeName("证书类型", "Certificate Type", "証明書タイプ"),
    typeSubName("证书子类类型", "Certificate Subtype", "証明書サブタイプ"),
    ceritifiCateName("证书名称", "Certificate Name", "証明書名"),
    ceritifiCateCode("证书编码", "Certificate Code", "証明書コード"),
    registerTime("注册日期", "Registration Date", "登録日"),
    expireTime("失效日期", "Expiration Date", "失効日"),
    registrationNo("注册号", "Registration Number", "登録番号"),
    specialty("专业", "Specialty", "専門"),
    i18nName("专业多语言字段", "Specialty Multilingual Field", "専門多言語フィールド"),
    issueAt("签发地", "Issued At", "発行場所"),
    accessProvince("入省备案", "Access Province Record", "入省記録"),
    isuse("是否限制使用", "Is Restricted Use", "使用制限あり"),
    isRegister("是否注册", "Is Registered", "登録済み"),
    remake("备注", "Remarks", "備考"),
    file("附件", "Attachment", "添付ファイル");

    public String zn;
    public String en;
    public String ja;

    CertificateDataTextEnum(String zn, String en, String ja) {
        this.zn=zn;
        this.en=en;
        this.ja=ja;
    }

    public static String  getLanguageTxt(String header,String enumValue){
        if (header.equals("zh-CN")){
            return   CertificateDataTextEnum.valueOf(enumValue).zn;
        }else if (header.equals("en-US")) {
            return   CertificateDataTextEnum.valueOf(enumValue).en;
        }else {
            return   CertificateDataTextEnum.valueOf(enumValue).ja;
        }

    }
}
