package com.caidaocloud.certificate.service.certificate.domain.repository;


import java.util.List;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepository;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateProjectDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateProjectQueryDto;
import com.caidaocloud.dto.PageResult;

public interface ICertificateProjectRepository extends BaseRepository<CertificateProjectDo> {

	void save(CertificateProjectDo certificateProjectDo);

	PageResult<CertificateProjectDo> selectPage(CertificateProjectQueryDto dto);

	List<CertificateProjectDo> selectByOrg(String organize, String bid);

	PageResult<CertificateProjectDo> loadByTime(long datetime, int pageSize, int pageNo);

	List<CertificateProjectDo> listById(List<String> proIds);
}