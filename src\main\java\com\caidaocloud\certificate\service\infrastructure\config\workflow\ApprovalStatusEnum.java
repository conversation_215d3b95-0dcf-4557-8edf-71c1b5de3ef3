package com.caidaocloud.certificate.service.infrastructure.config.workflow;

public enum ApprovalStatusEnum {
    IN_APPROVAL(0, "审批中","IN_APPROVAL","承認中"),
    PASSED(1, "审批通过","PASSED","承認済み"),
    REJECTED(2, "审批拒绝","refuse","承認拒否");

    private Integer index;
    private String name;
    private String en;
    private String ja;

    ApprovalStatusEnum(Integer index, String name,String en,String ja) {
        this.name = name;
        this.index = index;
        this.en = en;
        this.ja = ja;
    }

    public static String getName(int index) {
        for (ApprovalStatusEnum c : ApprovalStatusEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }
    public static String  getLanguageTxt(String index,String header) {
        String enumV=index.equals("0")?"IN_APPROVAL":index.equals("1")?"PASSED":"REJECTED";
        if (header.contains("zh-CN")) {
            return ApprovalStatusEnum.valueOf(enumV).name;
        } else if (header.contains("en-US")) {
            return ApprovalStatusEnum.valueOf(enumV).en;
        } else {
            return ApprovalStatusEnum.valueOf(enumV).ja;
        }
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
