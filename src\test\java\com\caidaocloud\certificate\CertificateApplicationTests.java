package com.caidaocloud.certificate;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import org.springframework.boot.test.context.SpringBootTest;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;

@SpringBootTest
class CertificateApplicationTests {
    public static void main(String[] args) {
        String data = String.valueOf(System.currentTimeMillis());
        String time = StringUtils.rightPad(data, 13, "0");
        String endTime=StringUtils.substring(data,0,5)+"86400";
        System.out.println(data);
        System.out.println(time);
        System.out.println(endTime);

    }

    @Test
    public void address(){
        String json = "[\n"
                + "  \"340000\",\n"
                + "  \"340100\",\n"
                + "  \"340102\"\n"
                + "]";
        Address address = FastjsonUtil.toObject(json, Address.class);
        Assert.assertEquals("340000/340100/340102/null",address.doText());
    }

}
