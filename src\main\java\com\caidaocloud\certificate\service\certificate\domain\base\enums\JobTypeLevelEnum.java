package com.caidaocloud.certificate.service.certificate.domain.base.enums;

/**
 * <AUTHOR>
 */
public enum JobTypeLevelEnum {
    /**
     * 1-职务族
     */
    JOB_FAMILY(1, "职务族"),

    /**
     * 2-职务类
     */
    JOB_FUNCTION(2, "职务类"),

    /**
     * 3-职务子类
     */
    JOB_SUB_FUNCTION(3, "职务子类");

    private Integer index;
    private String name;

    JobTypeLevelEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public static String getName(Integer index) {
        for (JobTypeLevelEnum c : JobTypeLevelEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
