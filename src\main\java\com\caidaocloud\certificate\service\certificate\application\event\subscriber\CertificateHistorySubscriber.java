package com.caidaocloud.certificate.service.certificate.application.event.subscriber;

import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpHistoryDo;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefMetadataDto;
import com.caidaocloud.hrpaas.paas.common.event.FormPublishEvent;
import com.caidaocloud.hrpaas.paas.common.feign.FormFeignClient;
import com.caidaocloud.msg.handler.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.configuration.WfFunctionConfiguration;
import com.caidaocloud.workflow.dto.WfMetaCallbackDto;
import com.caidaocloud.workflow.dto.WfMetaFunDto;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.enums.WfCallbackTimeTypeEnum;
import com.caidaocloud.workflow.enums.WfCallbackTypeEnum;
import com.caidaocloud.workflow.enums.WfFieldDataTypeEnum;
import com.caidaocloud.workflow.enums.WfFunctionPageJumpType;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.google.common.collect.ImmutableMap;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/4/24
 * 如果表单变动，对数据进行重新初始化
 * 未生效 后续研发自己处理吧
 */
@Slf4j
@Component
public class CertificateHistorySubscriber implements MessageHandler<FormPublishEvent> {
	@Resource
	private IWfRegisterFeign iWfRegisterFeign;
	@Resource
	private FormFeignClient formFeignClient;

	@Override
	public String topic() {
		return  FormPublishEvent.topic;
	}

	@Override
	public void handle(FormPublishEvent message) throws Exception {
		log.info("EmployeePageChangeSubscriber handle message: {}", message);
		if (!message.getFormCode().equals(CeritificateAndEmpHistoryDo.code)){
			log.error("message code与工作流历史表中的code 不匹配 ");
		}
		try {
			SecurityUserInfo securityUserInfo = new SecurityUserInfo();
			securityUserInfo.setUserId(0l);
			securityUserInfo.setTenantId(message.getTenantId());
			securityUserInfo.setEmpId(0L);
			SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
			List<WfMetaFunFormFieldDto> wfMetaFunFormFieldDtos = fileTableInit(message.getFormCode());
			doInitWfRegister(wfMetaFunFormFieldDtos);
			WfFunctionConfiguration.getFunFieldMap().putIfAbsent("CERTIFICATE-EMP", ImmutableMap.copyOf(wfMetaFunFormFieldDtos.stream()
					.collect(Collectors.toMap(e -> e.getCode(), e -> e, (v1, v2) -> v2))));
		}finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
	}

	private void doInitWfRegister(List<WfMetaFunFormFieldDto> wfMetaFunFormFieldDtos) {
		//员工证书申请
		WfMetaFunDto dto = new WfMetaFunDto("员工证书申请", "CERTIFICATE-EMP",
				WfFunctionPageJumpType.RELATIVE_PATH, "",
				"caidaocloud-certificate-service",
				"", "/api/certificate/v1/emp/getById", "",wfMetaFunFormFieldDtos );
		iWfRegisterFeign.registerFunction(dto);

		registerCallback("CERTIFICATE-EMP", "回调失败", "CERTIFICATE-FAIL", "/api/certificate/v1/history/fail");
		WfMetaCallbackDto wmc;

		registerCallback("CERTIFICATE-EMP", "回调成功", "CERTIFICATE-SUCCESS", "/api/certificate/v1/history/success");
		WfMetaCallbackDto wmcc;

	}
	private void registerCallback(String newSignFunCode, String name, String code, String apiPath) {
		WfMetaCallbackDto wmc = new WfMetaCallbackDto(name, code, Lists.list(newSignFunCode),
				"",
				apiPath,
				"caidaocloud-certificate-service",
				"",
				WfCallbackTypeEnum.RELATIVE_PATH,
				WfCallbackTimeTypeEnum.NOW);
		iWfRegisterFeign.registerCallback(wmc);
	}
	public List<WfMetaFunFormFieldDto> fileTableInit(String formCode){
		Result<FormDefDto> data = formFeignClient.getFormDefByCode(formCode);
		if (ObjectUtil.isEmpty(data)||ObjectUtil.isEmpty(data.getData())) {
			return new ArrayList<>();
		}
		List<WfMetaFunFormFieldDto> formFields = Lists.list(
				new WfMetaFunFormFieldDto("typeName", "证书类型", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("typeSubName", "证书子类型", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("ceritifiCateName", "证书名称", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("ceritifiCateCode", "证书编码", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("expireTime", "失效日期", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("registerTime", "注册日期", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("registrationNo", "注册号", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("i18nName", "专业国际化", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("issueAt", "签发地", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("accessProvince", "入省备案", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("isuse", "是否限制使用", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("isRegister", "是否注册", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("remake", "备注", WfFieldDataTypeEnum.Text),
				new WfMetaFunFormFieldDto("annex", "附件", WfFieldDataTypeEnum.Text)
		);
		List<FormDefMetadataDto> properties = data.getData().getProperties();
		properties.stream().forEach(t->{
			formFields.add(new WfMetaFunFormFieldDto(t.getProperty(), t.getName(),  WfFieldDataTypeEnum.Text));
		});

		return formFields;
	}
}
