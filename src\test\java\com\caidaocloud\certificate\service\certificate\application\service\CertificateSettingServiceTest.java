package com.caidaocloud.certificate.service.certificate.application.service;

import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateSettingModelDto;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CertificateSettingService 测试类
 * 验证重构后的DTO替代Map的功能
 */
@SpringBootTest
@ActiveProfiles("test")
public class CertificateSettingServiceTest {

    @Resource
    private CertificateSettingService certificateSettingService;

    @Test
    public void testSetMethodReturnsDto() {
        // 测试set方法返回CertificateSettingResultDto列表而不是Map列表
        String typeCode = "msg"; // 使用一个测试用的typeCode
        
        try {
            List<CertificateSettingModelDto> result = certificateSettingService.set(typeCode);
            
            // 验证返回类型正确
            assertNotNull(result, "结果不应为null");
            assertTrue(result instanceof List, "结果应该是List类型");
            
            // 如果有结果，验证DTO结构
            if (!result.isEmpty()) {
                CertificateSettingModelDto firstDto = result.get(0);
                assertNotNull(firstDto, "DTO不应为null");
                
                // 验证DTO包含必要的字段
                assertNotNull(firstDto.getName(), "name字段不应为null");
                assertNotNull(firstDto.getIdentifier(), "identifier字段不应为null");
                assertNotNull(firstDto.getProperties(), "properties字段不应为null");
                
                System.out.println("测试通过：成功返回CertificateSettingResultDto列表");
                System.out.println("第一个DTO - Name: " + firstDto.getName() + 
                                 ", Identifier: " + firstDto.getIdentifier() + 
                                 ", Properties count: " + firstDto.getProperties().size());
            } else {
                System.out.println("测试通过：返回空列表（可能是因为测试环境配置）");
            }
            
        } catch (Exception e) {
            fail("测试失败：" + e.getMessage());
        }
    }
}
