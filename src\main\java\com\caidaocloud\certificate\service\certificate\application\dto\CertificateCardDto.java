package com.caidaocloud.certificate.service.certificate.application.dto;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date 2024/5/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CertificateCardDto {
	private String empId;
	private List<CeritificateAndEmpDo> ceritificateList = new ArrayList<>();
}
