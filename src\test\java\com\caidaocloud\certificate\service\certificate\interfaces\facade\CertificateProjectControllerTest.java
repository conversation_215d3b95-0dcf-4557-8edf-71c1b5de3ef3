package com.caidaocloud.certificate.service.certificate.interfaces.facade;

import java.util.List;
import java.util.Map;

import javax.swing.Spring;

import com.caidaocloud.certificate.CertificateApplication;
import com.caidaocloud.certificate.service.certificate.application.service.CertificateProjectService;
import com.caidaocloud.certificate.service.certificate.application.service.CertificateTypeService;
import com.caidaocloud.certificate.service.certificate.application.service.QualifiedPerService;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.AvailableEmpQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateProjectDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateProjectQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateTypeQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordSaveDto;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CeritificateTopVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateTypeVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateUsageRecordPageVo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = CertificateApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class CertificateProjectControllerTest {

	private MockMvc mockMvc;

	@SpyBean
	private CertificateProjectService certificateProjectService;

	@Autowired
	private CertificateProjectController certificateProjectController;

	@Autowired
	private QualifiedPerService qualifiedPerService;

	@Autowired
	private QualifiedPerController qualifiedPerController;

	@Autowired
	private CertificateTypeService certificateTypeService;

	@Before
	public void setUp() {
		// MockitoAnnotations.initMocks(this);
		// mockMvc = MockMvcBuilders.standaloneSetup(certificateProjectController).build();
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setEmpId(0L);
		userInfo.setUserId(1923451746924549L);
		userInfo.setTenantId("1006");
		SecurityUserUtil.setSecurityUserInfo(userInfo);
	}

	private static final String ORG_ID = "1924302075435019";
	private static final String LEADER_EMP_ID = "1707073668872203";
	private static final String WORK_PLACE_ID = "1711993106307160";

	@Test
	public void createCertificateProjectTest() throws Exception {
		CertificateProjectDto dto = new CertificateProjectDto();
		dto.setOrganize(ORG_ID);
		dto.setEmpId(LEADER_EMP_ID);
		dto.setLocation(WORK_PLACE_ID);
		// Set up the necessary attributes for your dto

		// when(certificateProjectService.createCertificateProject(any(CertificateProjectDto.class))).thenReturn("projectId123");

		mockMvc.perform(post("/api/certificate/v1/project/create")
						.contentType(MediaType.APPLICATION_JSON)
						.content(FastjsonUtil.toJson(dto))) // Replace with actual JSON content or use a mapper to convert dto to JSON
				.andExpect(status().isOk());

		verify(certificateProjectService, times(1)).createCertificateProject(dto);
	}
	@Test
	public void getCertificateProjectPage_ShouldReturnSuccess() throws Exception {
		// Arrange
		CertificateProjectQueryDto queryDto = new CertificateProjectQueryDto();
		// Set up any necessary properties on queryDto

		// Act & Assert
		MvcResult result = mockMvc.perform(post("/api/certificate/v1/project/page")
						.contentType(MediaType.APPLICATION_JSON)
						.content(FastjsonUtil.toJson(queryDto)))
				.andReturn();

		// Verify that the service method was called with the correct parameters
		verify(certificateProjectService, times(1)).getCertificateProjectPage(queryDto);
		System.out.println(result.getResponse().getContentAsString());
	}

	public static final String CERTIFICATE_BID = "1924212885198853";
	public static final String CERTIFICATE_EMP_ID = "1929965170358276";
	public static final String CERTIFICATE_PROJECT_ID = "1925737905207303";
	public static final String CERTIFICATE_TYPE_DICT_ID = "807";

	@Test
	public void createCertificateUsageRecordTest() throws Exception {
		CertificateUsageRecordSaveDto saveDto = new CertificateUsageRecordSaveDto();
		saveDto.setCertificate(CERTIFICATE_BID);
		saveDto.setEmpCertificate(Lists.list(CERTIFICATE_EMP_ID));
		saveDto.setProjectId(CERTIFICATE_PROJECT_ID);
		saveDto.setType(CERTIFICATE_TYPE_DICT_ID);

		mockMvc.perform(post("/api/certificate/v1/project/record/create")
						.contentType(MediaType.APPLICATION_JSON)
						.content(FastjsonUtil.toJson(saveDto)))
				.andExpect(status().isOk());
		verify(certificateProjectService).createCertificateUsageRecord(any(CertificateUsageRecordSaveDto.class));
	}

	@Test
	public void getAllCertificateUsageRecordsByProjectTest() throws Exception {
		// Arrange
		CertificateUsageRecordQueryDto queryDto = new CertificateUsageRecordQueryDto("projectId", 1, 10);
		// PageResult<CertificateUsageRecordPageVo> expectedPageResult = mock(PageResult.class);
		queryDto.setProjectId(CERTIFICATE_PROJECT_ID);


		// Act & Assert
		MvcResult result = mockMvc.perform(post("/api/certificate/v1/project/record/page")
				.contentType(MediaType.APPLICATION_JSON)
				.content(FastjsonUtil.toJson(queryDto))).andReturn();


		verify(certificateProjectService, times(1))
				.getAllCertificateUsageRecordsByProject(any(CertificateUsageRecordQueryDto.class));
		System.out.println(result.getResponse().getContentAsString());
	}

	@Test
	public void addressQueryTest(){

	}

	@Test
	public  void qp_sort_test(){

		System.out.println(FastjsonUtil.toJson(qualifiedPerService.selectUseCertificate()));
	}

	@Test
	public void header_test(){
		String json = "{\"pageNo\":1,\"pageSize\":10,\"filterAvailable\":false}";
		System.out.println(FastjsonUtil.toJson(qualifiedPerController.pageList(FastjsonUtil.toObject(json, CeritificateAndEmpQueryDto.class), new MockHttpServletRequest())));

	}

	@Test
	public void type_list(){
		CertificateTypeQueryDto dto = new CertificateTypeQueryDto();
		dto.setStatus("0");
		List<CertificateTypeVo> aList = certificateTypeService.getList(dto);


		List<CeritificateTopVo> bList = qualifiedPerService.selectUseCertificate();

		List<Pair<String, String>> except = Sequences.sequence(aList).flatMap(CertificateTypeVo::getChildren)
				.map(type -> Pair.pair(type.getBid(), type.getName())).toList();

		List<Pair<String, String>> actual = Sequences.sequence(bList)
				.map(type -> Pair.pair(type.getBid(), type.getName())).toList();

		Result<PageResult<Map<String, Object>>> result = qualifiedPerController.pageList(new CeritificateAndEmpQueryDto(), new MockHttpServletRequest());
		Assert.assertEquals(except, actual);
	}


@Test
	public void availableTest(){
	String json = "{\"ceritifiCateBid\":\"2043297056757760\",\"ceritificateStatus\":\"0\",\"apiType\":\"2\",\"pageNo\":1,\"pageSize\":10,\"projectId\":\"2218574711347200\",\"startTime\":1750348800000,\"endTime\":1753804800000}";
	certificateProjectController.getAvailableEmployees(FastjsonUtil.toObject(json, AvailableEmpQueryDto.class));
}

}

