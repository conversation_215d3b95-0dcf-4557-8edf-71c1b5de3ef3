package com.caidaocloud.certificate.service.certificate.domain.repository;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.masterdata.entity.emp.EmpInfoEntity;

import java.util.List;

public interface IEmpInfoRepository extends BaseRepository<EmpInfoEntity> {

    List<EmpInfoEntity> queryListByEmpIds(List<String> empIds);

    List<EmpInfoEntity> queryList(CeritificateAndEmpQueryDto data, long currentTimeMillis);
}
