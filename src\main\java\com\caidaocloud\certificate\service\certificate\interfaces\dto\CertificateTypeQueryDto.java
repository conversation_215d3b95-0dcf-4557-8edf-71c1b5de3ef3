package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 * 证书类型查询dto
 **/
@Data
@Slf4j
public class CertificateTypeQueryDto extends BasePage implements Serializable   {
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("编码")
    private String code;
    @ApiModelProperty("层级")
    private Integer level;
    @ApiModelProperty("序列编码")
    private Integer sortNum;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("父级bid")
    private String pBid;
    @ApiModelProperty("bid")
    private String bid;
    @ApiModelProperty("关键字")
    private String keyWord;
}
