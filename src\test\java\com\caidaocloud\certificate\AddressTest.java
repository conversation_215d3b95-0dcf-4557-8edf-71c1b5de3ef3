package com.caidaocloud.certificate;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.util.FastjsonUtil;
import org.junit.Assert;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 * @date 2024/5/22
 */
public class AddressTest {
	@Test
	public void address(){
		String json = "[\n"
				+ "  \"340000\",\n"
				+ "  \"340100\",\n"
				+ "  \"340102\"\n"
				+ "]";
		Address address = FastjsonUtil.toObject(json, Address.class);
		Assert.assertEquals("340000/340100/340102/null",address.doText());
	}

	@Test
	public void t(){
		DataFilter dataFilter = DataFilter.eq("tenantId", "1")
				.andNe("deleted", Boolean.TRUE.toString())
				.andNe("empStatus", String.valueOf("1"))
				//表字段leaveDate的值小于等于我传的参数
				.and<PERSON>e("leaveDate", String.valueOf("1753002588705"));
		System.out.println(FastjsonUtil.toJson(dataFilter));
	}
}
