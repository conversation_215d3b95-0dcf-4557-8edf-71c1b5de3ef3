package com.caidaocloud.certificate.service.certificate.application.service;

import com.caidaocloud.certificate.service.certificate.application.feign.MasterDataEmpInfoFeign;
import com.caidaocloud.certificate.service.certificate.domain.base.dict.DictUtilService;
import com.caidaocloud.certificate.service.certificate.domain.base.util.LangUtil;
import com.caidaocloud.certificate.service.certificate.domain.base.util.ObjectConvertUtil;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.domain.base.util.WorkFlowUtil;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpHistoryDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateTypeDo;
import com.caidaocloud.certificate.service.certificate.domain.enums.BooleanValueEnum;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateDataTextEnum;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICeritificateAndEmpRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.*;
import com.caidaocloud.certificate.service.infrastructure.config.workflow.ApprovalStatusEnum;
import com.caidaocloud.certificate.service.infrastructure.config.workflow.dto.WfApprovalDto;
import com.caidaocloud.certificate.service.infrastructure.config.workflow.dto.WfTaskApproveDTO;
import com.caidaocloud.certificate.service.infrastructure.config.workflow.feign.IWfOperateFeignClient;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.workflow.dto.WfAttachmentDto;
import com.caidaocloud.hr.workflow.enums.WfAttachmentTypeEnum;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.paas.common.dto.FormDataDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefDto;
import com.caidaocloud.hrpaas.paas.common.feign.FormFeignClient;
import com.caidaocloud.masterdata.dto.EmpWorkInfoVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.*;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.beans.PropertyDescriptor;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/6 11:38
 **/
@Service
@Slf4j
public class CeritificateAndEmpHistoryService {
    @Resource
    private CertificateDo certificateDo;
    @Resource
    private IWfRegisterFeign iWfRegisterFeign;
    @Resource
    private CeritificateAndEmpHistoryDo historyDo;
    @Resource
    private FormFeignClient formFeignClient;
    @Autowired
    private IWfOperateFeignClient wfOperateFeignClient;

    /**
     * 证书人员管理列表查询 证书概览
     */
    public PageResult<CeritificateAndEmpHistoryDo> selectList(HistoryQueryDto vo) {
        HttpServletRequest request = WebUtil.getRequest();
        String header = request.getHeader("Accept-Language");
        PageResult<CeritificateAndEmpHistoryDo> ceritificateAndEmpHistoryDoPageResult = historyDo.queryList(vo);
        List<CeritificateAndEmpHistoryDo> items = ceritificateAndEmpHistoryDoPageResult.getItems();
        for (CeritificateAndEmpHistoryDo item : items) {
            CertificateDo certificate = certificateDo.selectById(item.getCeritifiCateBid());
            CertificateTypeDo typeDo = SpringUtil.getBean(CertificateTypeDo.class).selectById(item.getTypeBid());
            Map certificateI18n = FastjsonUtil.toObject(certificate.getI18nName(), Map.class);
            if (certificateI18n!=null&& certificate!=null){
                item.setCeritifiCateName(certificateI18n.get(header)==null? (String) certificateI18n.get("default") :(String)certificateI18n.get(header));
            }
            Map typeI18n = FastjsonUtil.toObject(typeDo.getI18nName(), Map.class);

            if (typeI18n!=null &&typeDo !=null){
                item.setTypeName(typeI18n==null||typeI18n.get(header)==null? (String) typeI18n.get("default") :(String)typeI18n.get(header));
            }
            //枚举类型
            EnumSimple isRegister = item.getIsRegister();
            isRegister.setText(BooleanValueEnum.getLanguageTxt(item.getIsRegister().getText(),header));
            item.setIsRegister(isRegister);
            EnumSimple isuse = item.getIsuse();
            isuse.setText(BooleanValueEnum.getLanguageTxt(item.getIsuse().getText(),header));
            item.setIsuse(isuse);
            //专业
            Map specialty = FastjsonUtil.toObject(item.getI18nName(), Map.class);
            if (specialty!=null ){
                item.setSpecialty(String.valueOf(specialty.getOrDefault(header,item.getSpecialty())));
            }
            //数据字典值
            DictSimple accessProvince = item.getAccessProvince();
            DictSimple issueAt = item.getIssueAt();
            DictUtilService dictUtilService=new DictUtilService();
            if (accessProvince !=null && accessProvince.getText() !=null && accessProvince.getValue()!=null){

                String langText = dictUtilService.getLangText(accessProvince.getValue(), accessProvince.getText(), header);
                accessProvince.setText(langText);
                item.setAccessProvince(accessProvince);
            }
            if (issueAt !=null && issueAt.getText() !=null && issueAt.getValue()!=null){

                String langText = dictUtilService.getLangText(issueAt.getValue(), issueAt.getText(), header);
                issueAt.setText(langText);
                item.setIssueAt(issueAt);
            }
            //审批状态
            EnumSimple approveStatus = item.getApproveStatus();
            approveStatus.setText(ApprovalStatusEnum.getLanguageTxt(approveStatus.getValue(),header));
            item.setApproveStatus(approveStatus);

        }
        return new PageResult<>(items,vo.getPageNo(),vo.getPageSize(),ceritificateAndEmpHistoryDoPageResult.getTotal());
    }

    @Async("taskExecutor")
    EmpWorkInfoVo getEntity(String empId) {
        return SpringUtil.getBean(MasterDataEmpInfoFeign.class).loadEmpWorkInfo(empId, System.currentTimeMillis()).getData();
    }

    /**
     * 新增编辑
     */
    public String save(CeritificateAndEmpHistoryDto vo) {

        CeritificateAndEmpDo cer = BeanUtil.convert(vo, CeritificateAndEmpDo.class);


        CertificateDo certificate = certificateDo.selectById(vo.getCeritifiCateBid());
        PreCheck.preCheckArgument(ObjectUtil.isEmpty(certificate), "证书不存在");
        EmpWorkInfoVo empInfoEntity = getEntity(vo.getEmpId());
        PreCheck.preCheckArgument(ObjectUtil.isEmpty(empInfoEntity), "员工不存在");
        PreCheck.preCheckArgument(ObjectUtil.isEmpty(empInfoEntity.getOrganize()), "组织不存在");
        cer.setOrganizeId(empInfoEntity.getOrganize());
        certificate.setEmpId(vo.getEmpId());
        certificate.update(certificate);

        if (StringUtils.isNotEmpty(vo.getFormId())) {
            FormDefDto data = formFeignClient.getFormDefById(vo.getFormId()).getData();
            FormDataDto formDataDto = new FormDataDto();
            formDataDto.setId(data.getId());
            formDataDto.setTargetId(data.getTarget());
            formDataDto.setPropertiesMap(vo.getFormDataMap());
            String dataid = formFeignClient.saveFormData(vo.getFormId(), formDataDto).getData();
            if (StringUtils.isEmpty(dataid)) {
                throw new ServerException("Form data saving failed");
            }
            vo.setFormData(dataid);

        }
        CeritificateAndEmpHistoryDo historyDo = BeanUtil.convert(vo, CeritificateAndEmpHistoryDo.class);
        historyDo.setI18nName(LangUtil.getI18nValue(vo.getSpecialty(), vo.getI18nName()));
        historyDo.setAttachFile(vo.getFile());
        historyDo.setBid(SnowUtil.nextId());
        historyDo.setBusinessKey(historyDo.getBid() +"_"+"CERTIFICATE-EMP");
        String DoId = historyDo.save(historyDo);
            //开启工作流
            try {
                certificateOpenWorkflow(cer,"CERTIFICATE-EMP",empInfoEntity,historyDo);

            } catch (Exception e) {
                log.error("saveCertificate workFlow error :{},{}", e.getMessage(), e);
                throw new ServerException(e.getMessage());
            }


            return DoId;

    }

    /**
     *开启工作流
     */
    @Async("taskExecutor")
    public void certificateOpenWorkflow(CeritificateAndEmpDo data, String code, EmpWorkInfoVo empInfoEntity, CeritificateAndEmpHistoryDo vo) {
        log.info("contractOpenWorkflow CertifivateAndEmpHistoryDo:{}", FastjsonUtil.toJson(data));
        WfBeginWorkflowDto workflowDto = new WfBeginWorkflowDto();
        workflowDto.setFuncCode(code);
        workflowDto.setApplicantId(data.getEmpId());
        workflowDto.setBusinessId(vo.getBid());
        workflowDto.setApplicantName(empInfoEntity.getName());
        // 业务单据事件时间
        workflowDto.setEventTime(System.currentTimeMillis());
        Result<?> wfResult = null;
        try {
            wfResult = iWfRegisterFeign.begin(workflowDto);
        } catch (Exception e) {
            log.error("contractOpenWorkflow beginWorkflow err,{}", e.getMessage(), e);
        }
        log.info("contractOpenWorkflow wfResult:{}", FastjsonUtil.toJson(wfResult));
        if (null == wfResult || !wfResult.isSuccess()) {
            Object msg = wfResult.getData();
            WorkFlowUtil.beginCallback(msg);
            throw new ServerException(msg.toString());
        }

        WorkFlowUtil.beginCallback(wfResult.getData());
    }

    public void callback(WfApprovalDto approvalDto){
        WfTaskApproveDTO wfTaskApproveDTO = new WfTaskApproveDTO();
        wfTaskApproveDTO.setTaskId(approvalDto.getTaskId());
        wfTaskApproveDTO.setChoice(approvalDto.getChoice());
        wfTaskApproveDTO.setComment(approvalDto.getComment()==null?"":approvalDto.getComment());
        try {
            Result<?> result = wfOperateFeignClient.approveTask(wfTaskApproveDTO);
            if (!result.isSuccess()) {
                PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
                throw  new ServerException("Approval Exception");
            }
        } catch (Exception e) {
            log.error("Approval Exception:{}", e.getMessage(), e);
            throw  new ServerException(e.getMessage());
        }

    }
    //工作流回调
    public void callback1( WfApprovalDto approvalDto) {
        if (approvalDto.getChoice()==null){
            log.info("回调测试"+approvalDto.toString());
           return;
        }
        // 设置回调用户信息
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId( UserContext.getTenantId());
        // 回调默认用户id为 0
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        try {
            String dataId = StringUtils.substringBefore(approvalDto.getBusinessKey(), "_");
            var data = historyDo.selectById(dataId);
            if (null == data || StringUtil.isEmpty(data.getBid())) {
                return;
            }

            EnumSimple enumSimple=new EnumSimple();
            ApprovalStatusEnum statusEnum ;
            switch (approvalDto.getChoice()) {
                case "APPROVE":
                    statusEnum = ApprovalStatusEnum.PASSED;
                    enumSimple.setText(statusEnum.getName());
                    enumSimple.setValue(String.valueOf(statusEnum.getIndex()));
                    data.setApproveStatus(enumSimple);


                    CeritificateAndEmpQueryDto dto=new CeritificateAndEmpQueryDto();
                    dto.setEmpId(data.getEmpId());
                    dto.setCeritifiCateName(data.getCeritifiCateName());
                    dto.setRegisterTime(data.getRegisterTime());
                    CeritificateAndEmpDo empDo = BeanUtil.convert(data, CeritificateAndEmpDo.class);
                    List<CeritificateAndEmpDo> list = SpringUtil.getBean(ICeritificateAndEmpRepository.class).queryList(dto);
                    if (CollectionUtils.isNotEmpty(list)){
                        CeritificateAndEmpDo ceritificateAndEmpDo = list.get(0);
                        BeanUtil.copyProperties(data, ceritificateAndEmpDo,"bid","i18nName");
                        if (data.getAttachFile()!=null){
                            ceritificateAndEmpDo.setAttachFile(data.getAttachFile());
                        }
                        CeritificateAndEmpDto convert = BeanUtil.convert(ceritificateAndEmpDo, CeritificateAndEmpDto.class);
                        convert.setBid(ceritificateAndEmpDo.getBid());
                        SpringUtil.getBean(CeritificateAndEmpService.class).saveOrUpdate(convert);
                    }else {
                        CeritificateAndEmpDto convert = BeanUtil.convert(empDo, CeritificateAndEmpDto.class);
                        convert.setBid(null);
                        SpringUtil.getBean(CeritificateAndEmpService.class).saveOrUpdate(convert);
                    }
                    break;
                case "REFUSE":
                    statusEnum = ApprovalStatusEnum.REJECTED;
                    enumSimple.setText(statusEnum.getName());
                    enumSimple.setValue(String.valueOf(statusEnum.getIndex()));
                    data.setApproveStatus(enumSimple);
                    break;
                default:
                    throw new ServerException("Unsupported approval callback type");
            }


            SpringUtil.getBean(CeritificateAndEmpHistoryDo.class).update(data);
        } catch (Exception e) {
            log.error("contract workflow callback err,{}", e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    public Map<String,Object> get(CeritificateAndEmpHistoryDto vo) {
        HttpServletRequest request = WebUtil.getRequest();
        String header = request.getHeader("Accept-Language");

        CeritificateAndEmpHistoryDo historyDo = this.historyDo.get(vo);
        EmpWorkInfoVo entity = getEntity(historyDo.getEmpId());
        historyDo.setWorkName(entity.getName());
        historyDo.setWorkNo(entity.getWorkno());
        CeritificateAndEmpHistoryDatailDto dto= initDto(historyDo,header);
        dto.setApplyName(entity.getName());
        dto.setLaunchName(entity.getName());
        if (dto.getI18nName()==null||ObjectUtil.isEmpty(dto.getI18nName())){
            Map<String,Object> i18nName=new HashMap<>();
            i18nName.put("default",dto.getSpecialty()==null?"":dto.getSpecialty());
            dto.setI18nName(i18nName);
        }

        List<WfBusinessDataDetailDto> list = new ArrayList<>();
        list.add(new WfBusinessDataDetailDto("workNo", CertificateDataTextEnum.getLanguageTxt(header,"workNo"),dto.getWorkNo(),null));
        list.add(new WfBusinessDataDetailDto("workName", CertificateDataTextEnum.getLanguageTxt(header,"workName"),dto.getWorkName(),null));
        list.add(new WfBusinessDataDetailDto("applyName", CertificateDataTextEnum.getLanguageTxt(header,"applyName"),dto.getApplyName(),null));
        list.add(new WfBusinessDataDetailDto("launchName", CertificateDataTextEnum.getLanguageTxt(header,"launchName"),dto.getLaunchName(),null));
        list.add(new WfBusinessDataDetailDto("typeName", CertificateDataTextEnum.getLanguageTxt(header,"typeName"),dto.getTypeName(),null));
        list.add(new WfBusinessDataDetailDto("typeSubName", CertificateDataTextEnum.getLanguageTxt(header,"typeSubName"),dto.getTypeSubName(),null));
        list.add(new WfBusinessDataDetailDto("ceritifiCateName", CertificateDataTextEnum.getLanguageTxt(header,"ceritifiCateName"),dto.getCeritifiCateName(),null));
        list.add(new WfBusinessDataDetailDto("ceritifiCateCode", CertificateDataTextEnum.getLanguageTxt(header,"ceritifiCateCode"),dto.getCeritifiCateCode(),null));
        list.add(new WfBusinessDataDetailDto("registerTime", CertificateDataTextEnum.getLanguageTxt(header,"registerTime"),initData(dto.getRegisterTime()),null));
        list.add(new WfBusinessDataDetailDto("expireTime", CertificateDataTextEnum.getLanguageTxt(header,"expireTime"),initData(dto.getRegisterTime()),null));
        list.add(new WfBusinessDataDetailDto("registrationNo", CertificateDataTextEnum.getLanguageTxt(header,"registrationNo"),dto.getRegistrationNo(),null));
        list.add(new WfBusinessDataDetailDto("specialty", CertificateDataTextEnum.getLanguageTxt(header,"specialty"),dto.getSpecialty(),null));
        list.add(new WfBusinessDataDetailDto("i18nName", CertificateDataTextEnum.getLanguageTxt(header,"i18nName"),dto.getI18nName(),null));
        list.add(new WfBusinessDataDetailDto("issueAt", CertificateDataTextEnum.getLanguageTxt(header,"issueAt"),dto.getIssueAt(),null));
        list.add(new WfBusinessDataDetailDto("accessProvince", CertificateDataTextEnum.getLanguageTxt(header,"accessProvince"),dto.getAccessProvince(),null));
        list.add(new WfBusinessDataDetailDto("isuse", CertificateDataTextEnum.getLanguageTxt(header,"isuse"),dto.getIsuse(),null));
        list.add(new WfBusinessDataDetailDto("isRegister", CertificateDataTextEnum.getLanguageTxt(header,"isRegister"),dto.getIsRegister(),null));
        list.add(new WfBusinessDataDetailDto("remake", CertificateDataTextEnum.getLanguageTxt(header,"remake"),dto.getRemake(),null));
        list.add(new WfBusinessDataDetailDto("file", CertificateDataTextEnum.getLanguageTxt(header,"file"),null,dto.getFile()));

        Map<String,Object> map=new HashMap<>();
        map.put("detailList",list);
        map.put("formId",historyDo.getFormId());
        map.put("dataId",historyDo.getFormData());
        return map;

    }

    /**
     * 时间格式化
     * @param data
     */
    private String initData(Object data) {
        if (ObjectUtil.isEmpty(data)||data==null){
            return "";
        }
        String timeData = data.toString();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        return sdf.format(new Date(Long.valueOf(timeData)));

    }

    private CeritificateAndEmpHistoryDatailDto initDto(CeritificateAndEmpHistoryDo historyDo, String header) {
        CeritificateAndEmpHistoryDatailDto dto = ObjectConvertUtil.convert(historyDo, CeritificateAndEmpHistoryDatailDto.class, (t, v1) ->
                v1.setI18nName(FastjsonUtil.toObject(t.getI18nName(), Map.class)));
        dto.setIsRegister(BooleanValueEnum.getLanguageTxt(historyDo.getIsRegister().getText(),header));
        dto.setIsuse(BooleanValueEnum.getLanguageTxt(historyDo.getIsuse().getText(),header));
        dto.setAccessProvince(historyDo.getAccessProvince().getText());
        dto.setIssueAt(historyDo.getIssueAt().getText());

        //CAIDAOM-3132 要求详情数据全部国际化
        CertificateDo certificate = certificateDo.selectById(historyDo.getCeritifiCateBid());
        CertificateTypeDo typeDo = SpringUtil.getBean(CertificateTypeDo.class).selectById(historyDo.getTypeBid());
        CertificateTypeDo subTypeDo = SpringUtil.getBean(CertificateTypeDo.class).selectById(historyDo.getTypeSubBid());
        Map certificateI18n = FastjsonUtil.toObject(certificate.getI18nName(), Map.class);
        if (certificateI18n!=null&& certificate!=null){
            dto.setCeritifiCateName(certificateI18n.get(header)==null? (String) certificateI18n.get("default") :(String)certificateI18n.get(header));
        }
        Map typeI18n = FastjsonUtil.toObject(typeDo.getI18nName(), Map.class);

        if (typeI18n!=null &&typeDo !=null){
            dto.setTypeName(typeI18n==null||typeI18n.get(header)==null? (String) typeI18n.get("default") :(String)typeI18n.get(header));
        }
        Map subTypeI18n = FastjsonUtil.toObject(subTypeDo.getI18nName(), Map.class);
        if (subTypeI18n!=null && subTypeDo!=null){
            dto.setTypeSubName(subTypeI18n==null||subTypeI18n.get(header)==null? (String) subTypeI18n.get("default") :(String)subTypeI18n.get(header));
        }

        if (historyDo.getAttachFile()!=null){
            if (historyDo.getAttachFile().getNames()!=null) {
                if (historyDo.getAttachFile().getNames() != null) {
                    List<String> names = historyDo.getAttachFile().getNames();
                    List<String> urls = historyDo.getAttachFile().getUrls();
                    List<WfAttachmentDto> entityList = new ArrayList<>();
                    for (int i = 0; i < names.size() && i < urls.size(); i++) {
                        entityList.add(new WfAttachmentDto(WfAttachmentTypeEnum.file, urls.get(i),names.get(i) ));
                    }
                    dto.setFile(entityList);
                }
            }
        }
        return dto;
    }

    public static Map<String, Object> objectToMap(Object obj) {
        BeanWrapper beanWrapper = new BeanWrapperImpl(obj);
        Map<String, Object> result = new HashMap<>();
        for (PropertyDescriptor propertyDescriptor : beanWrapper.getPropertyDescriptors()) {
            String propertyName = propertyDescriptor.getName();
            Object propertyValue = beanWrapper.getPropertyValue(propertyName);
            result.put(propertyName, propertyValue);
        }
        return result;
    }

    public String update(CeritificateAndEmpHistoryDo data) {
       String dataId = StringUtils.substringBefore(data.getBusinessKey(), "_");
        CeritificateAndEmpHistoryDo historyDo = this.historyDo.selectById(dataId);
        PreCheck.preCheckArgument(dataId==null,"data is not find");
       data.setSpecialty((String) FastjsonUtil.toObject(data.getI18nName(),Map.class).get("default"));
        CertificateDo certificate = certificateDo.selectById(data.getCeritifiCateBid());
        CertificateTypeDo typeDo1 = SpringUtil.getBean(CertificateTypeDo.class).selectById(data.getTypeBid());
        CertificateTypeDo subTypeDo = SpringUtil.getBean(CertificateTypeDo.class).selectById(data.getTypeSubBid());

        data.setCeritifiCateName(certificate.getName());
        data.setTypeName(typeDo1.getName());
        data.setTypeSubName(subTypeDo.getName());
        data.setBid(dataId);
        data.setEmpId(historyDo.getEmpId());
        this.historyDo.update(data);
       return data.getBid();
    }
}

