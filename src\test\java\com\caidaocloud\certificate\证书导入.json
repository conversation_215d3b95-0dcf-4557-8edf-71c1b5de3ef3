{"config": [{"fileImportAppend": false, "identifier": "entity.certificate.certificateType", "persistType": "paas", "properties": [{"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "证书类型代码", "property": "code", "required": true, "type": "COMMON", "writeProperty": "code"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "证书类型名称", "property": "name", "required": true, "type": "COMMON", "writeProperty": "name"}, {"columnOmittedEnabled": true, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "property": "level", "required": false, "type": "COMMON", "value": "1", "writeProperty": "level"}, {"columnOmittedEnabled": true, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "property": "status", "required": false, "type": "COMMON", "value": "0", "writeProperty": "status"}], "returnProperty": {"as": "certificateTypeBid", "property": "bid"}, "submitAlone": false, "update": {"datas": [{"ignoreWhenUpdate": [], "opr": "eq", "property": "code"}], "ignoreWhenUpdate": [], "opr": "and"}}, {"fileImportAppend": false, "identifier": "entity.certificate.certificateType", "persistType": "paas", "properties": [{"columnOmittedEnabled": true, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "imported": "certificateTypeBid", "property": "pBid", "required": false, "type": "COMMON", "writeProperty": "pBid"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "证书子类型代码", "property": "subCode", "required": true, "type": "COMMON", "writeAs": "code", "writeProperty": "code"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "证书子类型名称", "property": "subName", "required": true, "type": "COMMON", "writeAs": "name", "writeProperty": "name"}, {"columnOmittedEnabled": true, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "property": "subStatus", "required": false, "type": "COMMON", "value": "0", "writeAs": "status", "writeProperty": "status"}, {"columnOmittedEnabled": true, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "property": "SubLevel", "required": false, "type": "COMMON", "value": "2", "writeAs": "level", "writeProperty": "level"}], "returnProperty": {"as": "subTypeBid", "property": "bid"}, "submitAlone": false, "update": {"datas": [{"ignoreWhenUpdate": [], "opr": "eq", "property": "code"}], "ignoreWhenUpdate": [], "opr": "and"}}, {"fileImportAppend": false, "identifier": "entity.certificate.certificate", "persistType": "paas", "properties": [{"columnOmittedEnabled": true, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "imported": "subTypeBid", "property": "typeBid", "required": false, "type": "COMMON", "writeProperty": "typeBid"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "证书代码", "property": "code", "required": true, "type": "COMMON", "writeProperty": "code"}, {"columnOmittedEnabled": false, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "excelColumn": "证书名称", "property": "name", "required": true, "type": "COMMON", "writeProperty": "name"}, {"columnOmittedEnabled": true, "dataStartTime": false, "dictNameToCode": {}, "enumMap": {}, "property": "status", "required": false, "type": "COMMON", "value": "0", "writeProperty": "status"}], "submitAlone": false, "update": {"datas": [{"ignoreWhenUpdate": [], "opr": "eq", "property": "code"}], "ignoreWhenUpdate": [], "opr": "and"}}], "description": "1、按证书code+证书类别code+证书子类code校验是否存在，存在则更新；不存在则新增"}