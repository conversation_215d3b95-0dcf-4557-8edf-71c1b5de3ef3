package com.caidaocloud.certificate.service.certificate.application.cron;

import java.util.List;

import javax.annotation.Resource;

import com.caidaocloud.certificate.service.certificate.application.dto.MsgBusinessConfigDto;
import com.caidaocloud.certificate.service.certificate.application.dto.TenantDto;
import com.caidaocloud.certificate.service.certificate.application.feign.MaintenanceFeignClient;
import com.caidaocloud.certificate.service.certificate.application.service.CertificateProjectService;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateProjectDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateUsageRecordDo;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateProjectRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateProjectDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordUpdateDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.QualifiedPerQueryDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * 证书状态检测任务
 * <AUTHOR> Zhou
 * @date 2024/5/13
 */
@Component
@Slf4j
public class CertificateStatusTask {
	@Resource
	private MaintenanceFeignClient maintenanceFeignClient;
	@Resource
	private CertificateProjectService certificateProjectService;
	@Resource
	private ICertificateProjectRepository certificateProjectRepository;

	/**
	 * 任务执行逻辑
	 * @param jobParam 任务参数（可选）
	 * @return 执行结果
	 * @throws Exception 可能抛出的异常
	 */
	@XxlJob("certificateStatusJobHandler")
	public ReturnT<String> execute(String jobParam) throws Exception {
		// 检测证书状态的逻辑
		log.info("certificateStatusJobHandler start");
		for (TenantDto tenantDto : maintenanceFeignClient.tenantList().getData()) {
			try {
				SecurityUserInfo userInfo = new SecurityUserInfo();
				userInfo.setTenantId(tenantDto.getTenantId());
				userInfo.setUserId(0L);
				userInfo.setEmpId(0L);
				SecurityUserUtil.setSecurityUserInfo(userInfo);

				int pageNo = 1;
				while (true) {
					PageResult<CertificateUsageRecordDo> page = CertificateUsageRecordDo.loadByTime(DateUtil.getCurrentTimestamp(), 1000, pageNo++);
					if (page.getItems().isEmpty()) {
						break;
					}
					for (CertificateUsageRecordDo data : page.getItems()) {
						CertificateUsageRecordUpdateDto dto = FastjsonUtil.convertObject(data, CertificateUsageRecordUpdateDto.class);
						certificateProjectService.updateCertificateUsageRecord(dto);
					}
				}
			}
			catch (Exception e) {
				log.error("XxlJob CertificateStatusJobHandler error,tenantId={}", tenantDto.getTenantId(), e );
			}
			finally {
				SecurityUserUtil.removeSecurityUserInfo();
			}
		}

		log.info("certificateStatusJobHandler finished");

		// 返回执行结果，0为成功，非0为失败
		return ReturnT.SUCCESS;
	}

	/**
	 * 定时任务修改project状态
	 * @param jobParam 任务参数（可选）
	 * @return 执行结果
	 * @throws Exception 可能抛出的异常
	 */
	@XxlJob("certificateProjectStatusJobHandler")
	public ReturnT<String> projectStatusHandler(String jobParam) throws Exception {
		// 检测证书状态的逻辑
		log.info("certificateProjectStatusJobHandler start");
		for (TenantDto tenantDto : maintenanceFeignClient.tenantList().getData()) {
			try {
				SecurityUserInfo userInfo = new SecurityUserInfo();
				userInfo.setTenantId(tenantDto.getTenantId());
				userInfo.setUserId(0L);
				userInfo.setEmpId(0L);
				SecurityUserUtil.setSecurityUserInfo(userInfo);

				int pageNo = 1;
				while (true) {
					PageResult<CertificateProjectDo> page = certificateProjectRepository.loadByTime(DateUtil.getCurrentTimestamp(), 1000, pageNo++);
					if (page.getItems().isEmpty()) {
						break;
					}
					for (CertificateProjectDo data : page.getItems()) {
						CertificateProjectDto dto = FastjsonUtil.convertObject(data, CertificateProjectDto.class);
						certificateProjectService.updateCertificateProject(dto);
					}
				}
			}
			catch (Exception e) {
				log.error("XxlJob certificateProjectStatusJobHandler error,tenantId={}", tenantDto.getTenantId(), e );
			}
			finally {
				SecurityUserUtil.removeSecurityUserInfo();
			}
		}

		log.info("certificateProjectStatusJobHandler finished");

		// 返回执行结果，0为成功，非0为失败
		return ReturnT.SUCCESS;
	}
}