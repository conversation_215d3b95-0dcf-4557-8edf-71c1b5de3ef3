package com.caidaocloud.certificate.service.certificate.application.feign;

import java.util.List;

import com.caidaocloud.certificate.service.certificate.application.dto.TenantDto;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;

@Component
public class MaintenanceFeignFallBack implements MaintenanceFeignClient {
    @Override
    public Result<List<TenantDto>> tenantList() {
        return Result.fail();
    }
}
