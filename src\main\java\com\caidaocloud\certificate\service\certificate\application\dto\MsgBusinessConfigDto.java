package com.caidaocloud.certificate.service.certificate.application.dto;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date 2024/5/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MsgBusinessConfigDto {
	private String businessId;
	private String msgConfigId;

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		MsgBusinessConfigDto that = (MsgBusinessConfigDto) o;
		return Objects.equals(businessId, that.businessId) && Objects.equals(msgConfigId, that.msgConfigId);
	}

	@Override
	public int hashCode() {
		return Objects.hash(businessId, msgConfigId);
	}
}
