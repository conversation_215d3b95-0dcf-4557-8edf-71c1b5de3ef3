package com.caidaocloud.certificate.service.certificate.domain.entity;

import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpHistoryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.HistoryQueryDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.util.StringUtil;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/6 10:57
 **/
@Data
@Service
@Slf4j
public class CeritificateAndEmpHistoryDo extends DataEntity {
    public static final String IDENTIFIER = "entity.certificate.certificateAndEmpHistory";
    public static final String code = "certificate";
    /**
     * 员工id
     */
    private String empId;
    /**
     * 员工工号
     */
    private String workNo;
    /**
     * 员工姓名
     */
    private String workName;
    /**
     * 证书类型
     */
    private String typeName;
    /**
     * 证书类型bid
     */
    private String typeBid;
    /**
     * 证书子类类型
     */
    private String typeSubName;
    /**
     * 证书类型bid
     */
    private String typeSubBid;
    /**
     * 证书名称
     */
    private String ceritifiCateName;
    /**
     * 证书编码
     */
    private String ceritifiCateCode;
    /**
     * 证书id
     */
    private String ceritifiCateBid;
    private Long acquiredTime;
    /**
     * 注册日期
     */
    private String registerTime;
    /**
     * 失效日期
     */
    private String expireTime;
    /**
     * 注册号
     */
    private String registrationNo;
    /**
     * 专业
     */
    private String specialty;
    /**
     * 专业多语言字段
     */
    private String i18nName;
    /**
     * 签发地
     */
    private DictSimple issueAt;
    private String issueAuthority;
    /**
     * 入省备案
     */
    private DictSimple accessProvince;
    /**
     * 是否限制使用
     */
    private EnumSimple isuse;
    /**
     * 是否注册
     */
    private EnumSimple isRegister;
    /**
     * 备注
     */
    private String remake;
    /**
     * 附件
     */
    private Attachment attachFile;
    /**
     * 项目
     */
    private String proBid;
    /**
     *
     *组织id
     */
    private String organizeId;
    /**
     *
     *审批状态（0审批中1审批通过2审批拒绝）
     */
    private EnumSimple approveStatus;

    /**
     *
     *表单id
     */
    private String formId;
    /**
     *
     *表单数据
     */
    private String formData;
    /**
     *
     *流程key
     */
    private String businessKey;

    public PageResult<CeritificateAndEmpHistoryDo> queryList(HistoryQueryDto vo) {
        DataFilter filter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());

        if (StringUtil.isNotEmpty(vo.getName())) {
            filter = filter.and(DataFilter.eq("ceritifiCateName", vo.getName()));
        }
        if (StringUtil.isNotEmpty(vo.getEmpId())) {
            filter = filter.and(DataFilter.eq("empId", vo.getEmpId()));
        }
        if (StringUtil.isNotEmpty(vo.getApproveStatus())) {
            filter = filter.and(DataFilter.eq("approveStatus", vo.getApproveStatus()));
        }
        if (StringUtil.isNotEmpty(vo.getCreateTime())) {
            String timestamp = convertToStartOfDayTimestamp(Long.valueOf(vo.getCreateTime()));
            String startTime=StringUtils.rightPad(timestamp,13,"0");
            Long end = Long.valueOf(startTime) + 86400000;
            String endTime=String.valueOf(end);
            filter = filter.and(DataFilter.ge("createTime", startTime).andLe("createTime", endTime));
        }

        return DataQuery.identifier(CeritificateAndEmpHistoryDo.IDENTIFIER)
                .decrypt().specifyLanguage().queryInvisible()
                .limit(vo.getPageSize(), vo.getPageNo()).
                filter(filter, CeritificateAndEmpHistoryDo.class);
    }

    public CeritificateAndEmpHistoryDo selectById(String dataId) {
        return DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage().queryInvisible()
                .oneOrNull(dataId, CeritificateAndEmpHistoryDo.class);

    }

    public String save(CeritificateAndEmpHistoryDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        data.setCreateBy(userId);
        data.setCreateTime(System.currentTimeMillis());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
//        data.setBid(SnowUtil.nextId());
        data.setIdentifier(IDENTIFIER);

        String dataId = DataInsert.identifier(data.getIdentifier()).insert(data);
        return dataId;
    }

    public void update(CeritificateAndEmpHistoryDo data) {
        data.setCreateBy(UserContext.getUserId());
        data.setCreateTime(System.currentTimeMillis());
        data.setUpdateBy(UserContext.getUserId());
        data.setUpdateTime(System.currentTimeMillis());

        DataUpdate.identifier(IDENTIFIER).update(data);
    }

    public CeritificateAndEmpHistoryDo get(CeritificateAndEmpHistoryDto vo) {
        String bid = StringUtils.substringBefore(vo.getBusinessKey(), "_");
        return  selectById(bid);
    }

    public static String convertToStartOfDayTimestamp(long timestampInSeconds) {
        // 将10位时间戳转换为Instant对象
        Instant instant = Instant.ofEpochSecond(timestampInSeconds);

        // 将Instant转换为LocalDateTime（时区相关）
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        LocalDateTime localDateTime = zonedDateTime.toLocalDateTime();

        // 提取日期部分，并设置时间为0点0分0秒
        LocalDateTime startOfDay = localDateTime.toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = localDateTime.toLocalDate().atStartOfDay();

        // 将LocalDateTime转换回Instant
        Instant startOfDayInstant = startOfDay.atZone(ZoneId.systemDefault()).toInstant();

        // 将Instant转换回10位时间戳
        long startOfDayTimestamp = startOfDayInstant.getEpochSecond();

        return String.valueOf(startOfDayTimestamp);
    }

}
