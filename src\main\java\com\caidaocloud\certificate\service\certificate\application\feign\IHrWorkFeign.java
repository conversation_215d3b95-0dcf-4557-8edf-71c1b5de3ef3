package com.caidaocloud.certificate.service.certificate.application.feign;


import com.caidaocloud.certificate.service.certificate.interfaces.vo.ContractVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.org.OrgVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2022/11/24
 */
@FeignClient(value = "caidaocloud-hr-service",
		fallback = HrFeignFallback.class,
		configuration = FeignConfiguration.class
		)
public interface IHrWorkFeign {
	@GetMapping("/api/hr/org/v1/detail")
	Result<OrgVo> getDetail(@RequestParam("bid") String bid, @RequestParam("dataTime") Long dataTime);

	@GetMapping("/api/hr/emp/work/v1/detail")
	Result<EmpWorkInfoVo> getEmpWorkInfo(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime);

	@GetMapping("api/hr/emp/basic/v1/info/simple")
	Result<Map> getEmpInfoSimple(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime);

	@PostMapping("api/hr/emp/basic/v1/info/simple/list")
	Result<List<Map>> listEmpInfoSimple(@RequestBody List<String> empIds, @RequestParam("dataTime") Long dataTime);

	@GetMapping("api/hr/contract/v1/getEmpCurrentContract")
	Result<ContractVo> getEmpCurrentContract(@RequestParam("empId") String empId) ;
}
