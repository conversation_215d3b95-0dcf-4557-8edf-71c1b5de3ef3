package com.caidaocloud.certificate.service.certificate.application.cron;

import com.caidaocloud.certificate.service.certificate.application.dto.TenantDto;
import com.caidaocloud.certificate.service.certificate.application.feign.MaintenanceFeignClient;
import com.caidaocloud.certificate.service.certificate.application.service.CeritificateAndEmpService;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 证书人员状态刷新合同字段 权限字段
 * <AUTHOR>
 * @date 2024/5/13
 */
@Component
@Slf4j
public class CertificateAuthFieldTask {
	@Resource
	private MaintenanceFeignClient maintenanceFeignClient;
	@Resource
	private CeritificateAndEmpService ceritificateAndEmpService;

	/**
	 * 任务执行逻辑
	 * @param jobParam 任务参数（可选）
	 * @return 执行结果
	 * @throws Exception 可能抛出的异常
	 */
	@XxlJob("CertificateAuthFieldTaskHandler")
	public ReturnT<String> execute(String jobParam) throws Exception {
		// 检测证书状态的逻辑
		log.info("CertificateAuthFieldTaskHandler start");
		for (TenantDto tenantDto : maintenanceFeignClient.tenantList().getData()) {
			try {
				SecurityUserInfo userInfo = new SecurityUserInfo();
				userInfo.setTenantId(tenantDto.getTenantId());
				userInfo.setUserId(0L);
				userInfo.setEmpId(0L);
				SecurityUserUtil.setSecurityUserInfo(userInfo);

				int pageNo = 1;
				while (true) {
					CeritificateAndEmpQueryDto ceritificateAndEmpQueryDto=new CeritificateAndEmpQueryDto();
					ceritificateAndEmpQueryDto.setPageNo(pageNo++);
					ceritificateAndEmpQueryDto.setPageSize(1000);
					PageResult<CeritificateAndEmpDto> page = ceritificateAndEmpService.tablePage(ceritificateAndEmpQueryDto);
					if (page.getItems().isEmpty()) {
						break;
					}
					for (CeritificateAndEmpDto item : page.getItems()) {
						ceritificateAndEmpService.saveOrUpdate(item);
					}

				}
			}
			catch (Exception e) {
				log.error("XxlJob CertificateAuthFieldTaskHandler error,tenantId={}", tenantDto.getTenantId(), e );
			}
			finally {
				SecurityUserUtil.removeSecurityUserInfo();
			}
		}

		log.info("CertificateAuthFieldTaskHandler finished");

		// 返回执行结果，0为成功，非0为失败
		return ReturnT.SUCCESS;
	}

}