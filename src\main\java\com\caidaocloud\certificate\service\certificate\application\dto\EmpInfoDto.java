package com.caidaocloud.certificate.service.certificate.application.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("员工信息")
public class EmpInfoDto {
	@ApiModelProperty("业务ID")
	private String bid;

	@ApiModelProperty("员工工号")
	private String workno;

	@ApiModelProperty("员工姓名")
	private String name;

	@ApiModelProperty("英文名")
	private String enName;

	@ApiModelProperty("岗位")
	private String post;

	@ApiModelProperty("岗位名称")
	private String postTxt;

	@ApiModelProperty("所属组织")
	private String organize;

	@ApiModelProperty("所属组织名称")
	private String organizeTxt;

	@ApiModelProperty("公司邮箱")
	private String companyEmail;

	@ApiModelProperty("员工id")
	private String empId;

	@ApiModelProperty("手机号")
	private String phone;

	@ApiModelProperty("工作地")
	private String workplace;
	@ApiModelProperty("工作地名称")
	private String workplaceTxt;

	@ApiModelProperty("公司")
	private String company;
	@ApiModelProperty("公司名称")
	private String companyTxt;

	@ApiModelProperty("入职日期")
	private Long hireDate;

	@ApiModelProperty("员工类型")
	private DictSimple empType;


	@ApiModelProperty("离职日期")
	private Long leaveDate;

	@ApiModelProperty("员工状态")
	private EnumSimple empStatus;

	@ApiModelProperty("职务名称")
	private String jobTxt;

	@ApiModelProperty("社保缴纳地")
	private Address socialSecurity;


	/**
	 * 员工头像
	 */
	private Attachment photo;

	@ApiModelProperty("职级职等")
	private JobGradeRange jobGrade;
}
