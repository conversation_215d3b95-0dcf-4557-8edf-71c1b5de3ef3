package com.caidaocloud.certificate.service.certificate.application.service;

import java.util.Optional;

import javax.annotation.Resource;

import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateUsageRecordDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordImportDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordSaveDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordUpdateDto;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2024/5/23
 */
@Service
@Slf4j
public class CertificateProjectImportService {
	@Resource
	private CeritificateAndEmpService ceritificateAndEmpService;
	@Resource
	private CertificateProjectService certificateProjectService;


	public void importUsageRecord(CertificateUsageRecordImportDto dto) {
		log.info("证书登记使用情况导入，data={}", dto);
		Option<CeritificateAndEmpDo> option = ceritificateAndEmpService.loadActiveCertificateByEmp(dto.getEmp()
				.getEmpId(), dto.getCertificate());
		if (option.isEmpty()) {
			throw new ServerException("未找到该员工证书");
		}
		log.info("员工有效证书数据，data={}", option.get());
		String empCertificate = option.get().getBid();
		// Optional<CertificateUsageRecordDo> exist = CertificateUsageRecordDo.findByEmpCertificate(empCertificate);
		//
		// if (exist.isPresent()) {
		// 	CertificateUsageRecordUpdateDto saveDto = ObjectConverter.convert(dto, CertificateUsageRecordUpdateDto.class);
		// 	saveDto.setBid(exist.get().getBid());
		// 	log.info("证书已存在，updateData={}",saveDto);
		// 	certificateProjectService.updateCertificateUsageRecord(saveDto);
		// }
		// else {
			CertificateUsageRecordSaveDto saveDto = ObjectConverter.convert(dto, CertificateUsageRecordSaveDto.class);
			saveDto.setEmpCertificate(Lists.list(empCertificate));
			saveDto.setType(dto.getType().getValue());
			// log.info("证书不存在，saveData={}",saveDto);
			certificateProjectService.createCertificateUsageRecord(saveDto);
		// }

	}
}
