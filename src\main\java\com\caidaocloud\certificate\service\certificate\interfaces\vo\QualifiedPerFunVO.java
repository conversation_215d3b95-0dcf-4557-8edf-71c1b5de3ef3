package com.caidaocloud.certificate.service.certificate.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/8 17:23
 **/
@Data
public class QualifiedPerFunVO {
    //是否已锁定
    private Boolean IsColor;
    //证书是否有效
    private Boolean IsUse;
    @ApiModelProperty("是否限制使用")
    private Boolean IsRestricted;
    private String value;

    public String toExportTxt(){
        if (IsColor) {
            return "已锁定";
        }
        else if (IsRestricted) {
            return "限制使用";
        }
        else if (IsUse) {
            return "有证书";
        }
        return "-";
    }
}
