package com.caidaocloud.certificate.service.certificate.interfaces.facade;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.caidaocloud.certificate.service.certificate.application.service.CeritificateAndEmpService;
import com.caidaocloud.certificate.service.certificate.domain.base.util.ExcelUtils;
import com.caidaocloud.certificate.service.certificate.domain.base.util.TagProperty;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordShowDto;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CeritificateAndEmpVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateAndEmpExcelVo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/15 11:35
 * Describe : 证书人员管理
 **/
@RestController
@RequestMapping("/api/certificate/v1/emp")
@Api(value = "/api/certificate/v1/emp", description = "证书人员管理", tags = "v0.1")
@Slf4j
public class CeritificateAndEmpController {
    @Autowired
    private CeritificateAndEmpService ceritificateAndEmpService;

    @PostMapping("/list")
    @ApiOperation("证书概览")
    public Result<PageResult<CeritificateAndEmpVo>>selectList(@RequestBody CeritificateAndEmpQueryDto vo){
        return  Result.ok(ceritificateAndEmpService.selectList(vo));
    }

    @PostMapping("/listData")
    @ApiOperation("证书明细")
    public Result<PageResult<CeritificateAndEmpDto>> listData(@RequestBody CeritificateAndEmpQueryDto vo){
        return Result.ok(ceritificateAndEmpService.tablePage(vo));
    }
    @PostMapping("/saveOrUpdate")
    @ApiOperation("编辑/保存")
    public Result saveOrUpdate(@RequestBody CeritificateAndEmpDto vo){
//        ceritificateAndEmpService.checkOnly(vo);
        String bid= ceritificateAndEmpService.saveOrUpdate(vo);
        return Result.ok(bid);
    }
    @PostMapping("/delete")
    @ApiOperation("删除")
    public Result delete(@RequestBody CeritificateAndEmpDto vo){
        ceritificateAndEmpService.delete(vo);
        return Result.ok(true);
    }
    @PostMapping("/getById")
    @ApiOperation("人员证书使用详情")
    public Result getById(@RequestBody CeritificateAndEmpQueryDto vo){
        return Result.ok(ceritificateAndEmpService.getById(vo));
    }
    @PostMapping("/discard")
    @ApiOperation("作废")
    public Result discard(@RequestBody CeritificateAndEmpDto vo){
        ceritificateAndEmpService.discard(vo);
        return Result.ok(true);
    }
    @PostMapping("/export")
    @ApiOperation("导出")
    public void export(@RequestBody CeritificateAndEmpQueryDto dto, HttpServletResponse response){
        List<CertificateAndEmpExcelVo> exportList = ceritificateAndEmpService.selectExportRecordPage(dto);
        List<ExcelExportEntity> colList = new ArrayList<>();
        for (TagProperty tagProperty : ceritificateAndEmpService.installContractRecordExportProperty()) {
            ExcelExportEntity exprortEntity = new ExcelExportEntity(tagProperty.getPropertyTxt(), tagProperty.getProperty(), 15);
            exprortEntity.setOrderNum(tagProperty.getOrder());
            colList.add(exprortEntity);
        }
        try {
            ExcelUtils.downloadDataListMapExcel(colList, CollectionUtils.isEmpty(exportList) ? Lists.newArrayList() : exportList, "证书人员管理", response);
        } catch (Exception e) {
            log.error("download approval List excel err.{}", e.getMessage(), e);
        }
    }


    @PostMapping("/detail")
    @ApiOperation("查看证书使用情况")
    public Result<PageResult<CertificateUsageRecordShowDto>> detail(@RequestBody CeritificateAndEmpQueryDto vo){
        return Result.ok(ceritificateAndEmpService.getDetail(vo));
    }


    @ApiOperation("证书类型下拉表")
    @GetMapping("/dropList")
    public Result dropList(@RequestParam("bid") String bid) {
        return Result.ok( ceritificateAndEmpService.dropList(bid));
    }

    @PostMapping("/selectById")
    @ApiOperation("人员证书使用详情")
    public Result selectById(@RequestBody CeritificateAndEmpQueryDto vo){
        return Result.ok(ceritificateAndEmpService.selectByid(vo));
    }
    @GetMapping("/getByEmpId")
    @ApiOperation("人员详情")
    public Result getByEmpId(@RequestParam("empId")String empId){
        return Result.ok(ceritificateAndEmpService.getByEmpId(empId));
    }

    @PostMapping("/judge")
    @ApiOperation("校验接口")
    public Result judge(@RequestBody CeritificateAndEmpDto dto){
        return Result.ok(ceritificateAndEmpService.judge(dto));
    }

    @PostMapping("/flush")
    @ApiOperation("刷新状态接口")
    public Result flush(@RequestBody CeritificateAndEmpDto dto){
        return Result.ok(ceritificateAndEmpService.flush(dto));
    }

    @PostMapping("/test")
    @ApiOperation("用来触发自定义表单注册到工作流用到")
    public Result test(){
        ceritificateAndEmpService.test();
        return Result.ok();
    }

}
