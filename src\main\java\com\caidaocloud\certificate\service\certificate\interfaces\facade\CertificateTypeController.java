package com.caidaocloud.certificate.service.certificate.interfaces.facade;

import com.caidaocloud.certificate.service.certificate.application.service.CertificateTypeService;
import com.caidaocloud.certificate.service.certificate.domain.base.dto.StatusOptDto;
import com.caidaocloud.certificate.service.certificate.domain.base.enums.StatusEnum;
import com.caidaocloud.certificate.service.certificate.domain.base.util.LangUtil;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateTypeDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateTypeDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateTypeQueryDto;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:44
 **/
@RestController
@RequestMapping("/api/certificate/v1/Type")
@Api(value = "/api/certificate/v1/Type", description = "证书类型管理", tags = "v0.1")
public class CertificateTypeController {

    @Autowired
    private CertificateTypeService certificateTypeService;

    private void preCheckArgument(CertificateTypeDto dto) {
        PreCheck.preCheckArgument(StringUtils.isBlank(dto.getName()), "名称为空");
        PreCheck.preCheckArgument(null == dto.getCode(), "代码为空");
    }

    @PostMapping("/getList")
    @ApiOperation("层级列表")
    public Result getList(@RequestBody CertificateTypeQueryDto dto) {
        List treeData = certificateTypeService.getList(dto);
        return Result.ok(treeData);
    }

    @GetMapping("/getTreeData")
    @ApiOperation("证书类型和证书树形结构")
    public Result getTreeData() {
        List treeData = certificateTypeService.getTreeData();
        return Result.ok(treeData);
    }

    @PostMapping("/list")
    @ApiOperation("列表/下拉列表")
    public Result selectList(@RequestBody CertificateTypeQueryDto dto) {
        List<CertificateTypeDo> list = certificateTypeService.selectList(dto);
        return Result.ok(list);
    }

    @PostMapping("/add")
    @ApiOperation("新增")
    public Result add(@RequestBody CertificateTypeDto dto) {
        preCheckArgument(dto);
        CertificateTypeDo data = ObjectConverter.convert(dto, CertificateTypeDo.class);
        data.setI18nName(LangUtil.getI18nValue(data.getName(), dto.getI18nName()));
        return Result.ok(certificateTypeService.add(data));
    }

    @ApiOperation("查看证书详情")
    @PostMapping("/getById")
    public Result getDetail(@RequestBody CertificateTypeDto dto) {
        PreCheck.preCheckArgument(null == dto.getBid(), "bid为空");
        CertificateTypeDo data = certificateTypeService.getById(dto.getBid());
        // CertificateTypeVo datavo = ObjectConvertUtil.convert(data,
        // CertificateTypeVo.class,
        // (it, v1) -> {
        // v1.setI18nName(FastjsonUtil.toObject(it.getI18nName(), Map.class));
        // });
        return ResponseWrap.wrapResult(data);
    }

    @PostMapping("/edit")
    @ApiOperation("修改")
    public Result update(@RequestBody CertificateTypeDto dto) {
        PreCheck.preCheckArgument(null == dto.getBid(), "bid为空");
        preCheckArgument(dto);
        CertificateTypeDo data = ObjectConverter.convert(dto, CertificateTypeDo.class);
        data.setI18nName(LangUtil.getI18nValue(data.getName(), dto.getI18nName()));
        certificateTypeService.update(data);
        return Result.ok(true);
    }

    @ApiOperation("删除证书类型")
    @PostMapping("/delete")
    public Result delete(@RequestBody CertificateTypeDto dto) {
        PreCheck.preCheckArgument(null == dto.getBid(), "bid为空");
        CertificateTypeDo data = new CertificateTypeDo();
        data.setBid(dto.getBid());
        certificateTypeService.delete(data);
        return Result.ok(true);
    }

    @ApiOperation("启用或停用职务")
    @PostMapping("/updateStatus")
    public Result updateStatus(@RequestBody StatusOptDto dto) {
        dto.preCheckArgument();
        CertificateTypeDo data = ObjectConverter.convert(dto, CertificateTypeDo.class);
        if (StatusEnum.ENABLED.getIndex().equals(dto.getStatus())) {
            certificateTypeService.enable(data);
        } else {
            certificateTypeService.disable(data);
        }
        return Result.ok(true);
    }
}
