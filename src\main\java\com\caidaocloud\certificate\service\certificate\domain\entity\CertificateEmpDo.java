package com.caidaocloud.certificate.service.certificate.domain.entity;

import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.certificate.service.certificate.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.google.common.collect.Lists;
import groovy.util.logging.Slf4j;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/20 16:55
 * 消息通知
 **/
@Data
@Slf4j
@Service
@Deprecated
public class CertificateEmpDo extends DataEntity {

    private static final String IDENTIFIER = "entity.certificate.certificateEmpSet";

    /**
     * 类型code（资格人员code=qua,明细=emp）
     */
    private String typeCode;
    /**
     * 字段名称
     */
    private String name;
    /**
     * true不可更改
     */
    private Boolean change;
    /**
     * 备用字段
     */
    private String remark;



    /**
     * 保存
     * @param data
     */
    public void save(List<CertificateEmpDo> data) {
        List<CertificateEmpDo> certificateEmpDos = selectList(data.get(0));
        for (CertificateEmpDo empDo : data) {
            empDo.setBid(SnowUtil.nextId());
            if (certificateEmpDos.contains(empDo)){
                continue;
            }
            DataInsert.identifier(IDENTIFIER).insert(empDo);
        }

    }

    /**
     * 修改
     * @param data
     */
    public void update(CertificateEmpDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
         CertificateEmpDo dbData = selectById(data.getBid());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        DataEntity.initFieldValue(IDENTIFIER, BusinessEventTypeEnum.UPDATE, data, dbData);
        DataUpdate.identifier(IDENTIFIER).update(data);
    }

    /**
     * 查看详情
     * @param bid
     * @return
     */
    public  CertificateEmpDo selectById(String bid) {
        return DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage().queryInvisible()
                .oneOrNull(bid,  CertificateEmpDo.class);
    }
    /**
     * 删除
     *
     * @param data
     */
    public void delete( CertificateEmpDo data) {
        DataDelete.identifier(IDENTIFIER).softDelete(data.getBid(), System.currentTimeMillis());
    }

    /**
     * 列表查询
     * @param dto
     * @return
     */
    public List< CertificateEmpDo> selectList( CertificateEmpDo dto) {
        DataFilter filter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());
        if (dto.getTypeCode()!=null){
            filter = filter.and(DataFilter.eq("typeCode", dto.getTypeCode()));
        }
        PageResult< CertificateEmpDo> pageResult = DataQuery.identifier(IDENTIFIER).decrypt().specifyLanguage()
                .queryInvisible().filter(filter,  CertificateEmpDo.class);

        return null != pageResult ? pageResult.getItems() : Lists.newArrayList();
    }

    }
