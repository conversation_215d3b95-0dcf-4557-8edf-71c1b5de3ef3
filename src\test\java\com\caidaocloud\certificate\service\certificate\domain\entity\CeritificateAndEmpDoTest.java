package com.caidaocloud.certificate.service.certificate.domain.entity;

import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateStatusEnum;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateUseStatusEnum;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * 证书人员管理实体测试类
 * 测试重构后的状态设置方法
 */
public class CeritificateAndEmpDoTest {

    @Test
    public void testSetStatusAndUseStatus_ValidCertificate() {
        // 创建测试对象
        CeritificateAndEmpDo certificate = new CeritificateAndEmpDo();
        certificate.setExpireTime(null); // 永久有效
        certificate.setProBid(null); // 未被项目使用

        // 执行状态设置
        certificate.checkStatusAndUseStatus();

        // 验证证书状态
        assertEquals(CertificateStatusEnum.VALID.getValue(), certificate.getStatus().getValue());
        assertEquals(CertificateStatusEnum.VALID.getText(), certificate.getStatus().getText());
        assertEquals(CertificateStatusEnum.VALID.getValue(), certificate.getApproveStatus().getValue());

        // 验证使用状态
        assertEquals(CertificateUseStatusEnum.AVAILABLE.getValue(), certificate.getUseStatus().getValue());
        assertEquals(CertificateUseStatusEnum.AVAILABLE.getText(), certificate.getUseStatus().getText());
    }

    @Test
    public void testSetStatusAndUseStatus_ExpiredCertificate() {
        // 创建测试对象
        CeritificateAndEmpDo certificate = new CeritificateAndEmpDo();
        certificate.setExpireTime("1000000000000"); // 已过期的时间戳
        certificate.setProBid(null);

        // 执行状态设置
        certificate.checkStatusAndUseStatus();

        // 验证证书状态
        assertEquals(CertificateStatusEnum.INVALID.getValue(), certificate.getStatus().getValue());
        assertEquals(CertificateStatusEnum.INVALID.getText(), certificate.getStatus().getText());
        assertEquals(CertificateStatusEnum.INVALID.getValue(), certificate.getApproveStatus().getValue());

        // 验证使用状态
        assertEquals(CertificateUseStatusEnum.AVAILABLE.getValue(), certificate.getUseStatus().getValue());
        assertEquals(CertificateUseStatusEnum.AVAILABLE.getText(), certificate.getUseStatus().getText());
    }

    @Test
    public void testSetStatusAndUseStatus_InUseCertificate() {
        // 创建测试对象
        CeritificateAndEmpDo certificate = new CeritificateAndEmpDo();
        certificate.setExpireTime(null);
        certificate.setProBid("project123"); // 被项目使用

        // 执行状态设置
        certificate.checkStatusAndUseStatus();

        // 验证证书状态
        assertEquals(CertificateStatusEnum.VALID.getValue(), certificate.getStatus().getValue());
        assertEquals(CertificateStatusEnum.VALID.getText(), certificate.getStatus().getText());

        // 验证使用状态
        assertEquals(CertificateUseStatusEnum.IN_USE.getValue(), certificate.getUseStatus().getValue());
        assertEquals(CertificateUseStatusEnum.IN_USE.getText(), certificate.getUseStatus().getText());
    }

    @Test
    public void testSetStatusForDto() {
        // 创建测试DTO对象
        CeritificateAndEmpDto dto = new CeritificateAndEmpDto();
        dto.setExpireTime(null);
        dto.setProBid(null);

        // // 执行状态设置
        // CeritificateAndEmpDo.setStatusForDto(dto);

        // 验证状态设置
        assertNotNull(dto.getStatus());
        assertEquals(CertificateStatusEnum.VALID.getValue(), dto.getStatus().getValue());
        assertEquals(CertificateStatusEnum.VALID.getText(), dto.getStatus().getText());

        assertNotNull(dto.getUseStatus());
        assertEquals(CertificateUseStatusEnum.AVAILABLE.getValue(), dto.getUseStatus().getValue());
        assertEquals(CertificateUseStatusEnum.AVAILABLE.getText(), dto.getUseStatus().getText());
    }

    @Test
    public void testCertificateStatusEnum() {
        // 测试枚举值
        assertEquals("0", CertificateStatusEnum.VALID.getValue());
        assertEquals("有效", CertificateStatusEnum.VALID.getText());
        assertEquals("1", CertificateStatusEnum.INVALID.getValue());
        assertEquals("无效", CertificateStatusEnum.INVALID.getText());

        // 测试toEnumSimple方法
        EnumSimple validEnum = CertificateStatusEnum.VALID.toEnumSimple();
        assertEquals("0", validEnum.getValue());
        assertEquals("有效", validEnum.getText());

        // 测试fromValue方法
        assertEquals(CertificateStatusEnum.VALID, CertificateStatusEnum.fromValue("0"));
        assertEquals(CertificateStatusEnum.INVALID, CertificateStatusEnum.fromValue("1"));
        assertEquals(CertificateStatusEnum.VALID, CertificateStatusEnum.fromValue("unknown")); // 默认值
    }

    @Test
    public void testCertificateUseStatusEnum() {
        // 测试枚举值
        assertEquals("0", CertificateUseStatusEnum.AVAILABLE.getValue());
        assertEquals("可使用", CertificateUseStatusEnum.AVAILABLE.getText());
        assertEquals("1", CertificateUseStatusEnum.IN_USE.getValue());
        assertEquals("使用中", CertificateUseStatusEnum.IN_USE.getText());

        // 测试toEnumSimple方法
        EnumSimple availableEnum = CertificateUseStatusEnum.AVAILABLE.toEnumSimple();
        assertEquals("0", availableEnum.getValue());
        assertEquals("可使用", availableEnum.getText());

        // 测试fromValue方法
        assertEquals(CertificateUseStatusEnum.AVAILABLE, CertificateUseStatusEnum.fromValue("0"));
        assertEquals(CertificateUseStatusEnum.IN_USE, CertificateUseStatusEnum.fromValue("1"));
        assertEquals(CertificateUseStatusEnum.AVAILABLE, CertificateUseStatusEnum.fromValue("unknown")); // 默认值
    }
}
