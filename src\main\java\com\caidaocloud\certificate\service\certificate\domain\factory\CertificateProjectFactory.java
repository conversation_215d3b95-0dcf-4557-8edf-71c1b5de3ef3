package com.caidaocloud.certificate.service.certificate.domain.factory;

import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateProjectDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateUsageRecordDo;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICeritificateAndEmpRepository;
import com.caidaocloud.certificate.service.certificate.domain.repository.IOrgRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateProjectDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordSaveDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordUpdateDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.masterdata.entity.org.OrgEntity;
import com.caidaocloud.util.ObjectConverter;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CertificateProjectFactory {
    @Autowired
    private IOrgRepository orgRepository;
    @Autowired
    private ICeritificateAndEmpRepository ceritificateAndEmpRepository;

    public  CertificateProjectDo createProject(CertificateProjectDto dto) {
        CertificateProjectDo project = ObjectConverter.convert(dto, CertificateProjectDo.class);
        project.setProjectName(orgRepository.loadOrg(dto.getOrganize()).map(OrgEntity::getName).orElseThrow(() -> new RuntimeException("Organization not found")));
        return project;
    }

    public  CertificateProjectDo convert2Entity(CertificateProjectDto dto, CertificateProjectDo existingProject) {
        BeanUtils.copyProperties(dto, existingProject, "bid");
        existingProject.setProjectName(orgRepository.loadOrg(dto.getOrganize()).map(OrgEntity::getName).orElseThrow(() -> new RuntimeException("Organization not found")));
        return existingProject;

    }

    public CertificateUsageRecordDo createUsageRecord(CertificateUsageRecordSaveDto dto, String empCertificate) {
        CertificateUsageRecordDo certificateUsageRecord = ObjectConverter.convert(dto, CertificateUsageRecordDo.class);
        certificateUsageRecord.setStartDate(dto.getStartDate() == null ? 0 : dto.getStartDate());
        certificateUsageRecord.setEndDate(dto.getEndDate() == null ? DateUtil.MAX_TIMESTAMP : dto.getEndDate());
        certificateUsageRecord.setEmpCertificate(empCertificate);

        DictSimple type = new DictSimple();
        type.setValue(dto.getType());
        certificateUsageRecord.setType(type);

        CeritificateAndEmpDo ceritificateAndEmpDo = ceritificateAndEmpRepository.selectById(empCertificate, CeritificateAndEmpDo.IDENTIFIER);
        certificateUsageRecord.setEmpId(ceritificateAndEmpDo.getEmpId());
        return certificateUsageRecord;
    }

    public CertificateUsageRecordDo updateUsageRecord(CertificateUsageRecordUpdateDto dto) {
        CertificateUsageRecordDo certificateUsageRecord = ObjectConverter.convert(dto, CertificateUsageRecordDo.class);
        certificateUsageRecord.setStartDate(dto.getStartDate() == null ? 0 : dto.getStartDate());
        certificateUsageRecord.setEndDate(dto.getEndDate() == null ? DateUtil.MAX_TIMESTAMP : dto.getEndDate());
        return certificateUsageRecord;
    }
}
