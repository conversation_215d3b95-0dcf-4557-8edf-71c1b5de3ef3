package com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateProjectDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateUsageRecordDo;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateProjectRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateProjectQueryDto;
import com.caidaocloud.dto.FilterFunction;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import org.apache.commons.collections.CollectionUtils;

import org.springframework.stereotype.Repository;

@Repository
public class CertificateProjectRepositoryImpl  extends BaseRepositoryImpl<CertificateProjectDo> implements ICertificateProjectRepository{

	@Override
	public void save(CertificateProjectDo certificateProjectDo) {
		if (certificateProjectDo.getBid() == null) {
			certificateProjectDo.setBid(SnowUtil.nextId());
			DataInsert.identifier(certificateProjectDo.getIdentifier()).insert(certificateProjectDo);
		}
		else {
         DataUpdate.identifier(certificateProjectDo.getIdentifier()).update(certificateProjectDo);
		}
	}

	@Override
	public PageResult<CertificateProjectDo> selectPage(CertificateProjectQueryDto dto) {
		DataFilter dataFilter = DataFilter.ne("deleted", Boolean.TRUE.toString())
				.andRegexIf("projectName", dto.getProjectName(), () -> dto.getProjectName() != null);
		DataFilter filter = (DataFilter) dto.doDataFilter(dto.getFilters(), dataFilter);
		return DataQuery.identifier(CertificateProjectDo.IDENTIFIER).specifyLanguage().queryInvisible().decrypt()
				.limit(dto.getPageSize(), dto.getPageNo())
				.filter(filter, CertificateProjectDo.class);
	}

	@Override
	public List<CertificateProjectDo> selectByOrg(String organize, String bid) {
		return DataQuery.identifier(CertificateProjectDo.IDENTIFIER).specifyLanguage().queryInvisible().decrypt()
				.filter(DataFilter.eq("organize", organize)
						.andNeIf("bid", bid, () -> bid != null), CertificateProjectDo.class).getItems();
	}

	@Override
	public PageResult<CertificateProjectDo> loadByTime(long datetime, int pageSize, int pageNo) {
		String date = String.valueOf(datetime);
		DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
				.and(DataFilter.eq("startDate", date)
						.orEq("endDate", date)
						.orEq("certificateStartDate", date)
						.orEq("certificateEndDate", date));
		return DataQuery.identifier(CertificateProjectDo.IDENTIFIER).specifyLanguage().queryInvisible().decrypt()
				.limit(pageSize, pageNo)
				.filter(filter, CertificateProjectDo.class);
	}

	@Override
	public List<CertificateProjectDo> listById(List<String> proIds) {
		if (CollectionUtils.isEmpty(proIds)) {
			return new ArrayList<>();
		}
		return DataQuery.identifier(CertificateProjectDo.IDENTIFIER).specifyLanguage().queryInvisible().decrypt()
				.filter(DataFilter.in("bid", proIds), CertificateProjectDo.class).getItems();
	}
}
