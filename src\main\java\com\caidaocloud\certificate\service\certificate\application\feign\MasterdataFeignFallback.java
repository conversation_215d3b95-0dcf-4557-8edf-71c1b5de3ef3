package com.caidaocloud.certificate.service.certificate.application.feign;

import java.util.List;

import com.caidaocloud.certificate.service.certificate.application.dto.EmpInfoDto;
import com.caidaocloud.certificate.service.certificate.application.dto.EmpSearchDto;
import com.caidaocloud.masterdata.dto.EmpWorkInfoVo;
import com.caidaocloud.masterdata.entity.emp.PrivateInfoEntity;
import com.caidaocloud.web.Result;

/**
 *
 * <AUTHOR>
 * @date 2024/4/25
 */
public class MasterdataFeignFallback implements MasterDataEmpInfoFeign{
	@Override
	public Result<List<EmpInfoDto>> loadEmpInfoList(EmpSearchDto dto) {
		return null;
	}

	@Override
	public Result<EmpWorkInfoVo> loadEmpWorkInfo(String empId, Long datetime) {
		return null;
	}

	@Override
	public Result<PrivateInfoEntity> loadEmpPrivateInfo(String empId) {
		return null;
	}

	@Override
	public Result<PrivateInfoEntity> getEmpPrivateInfo(String empId, Boolean isFilter) {
		return null;
	}
}
