package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.certificate.service.certificate.domain.base.enums.WfDetailEnum;
import com.caidaocloud.hr.workflow.dto.WfAttachmentDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class WfBusinessDataDetailDto {

    @ApiModelProperty("字段属性")
    private String key;
    @ApiModelProperty("字段名称")
    private String text;
    @ApiModelProperty("字段值")
    private Object value;
    @ApiModelProperty("字段类型")
    private WfDetailEnum type;
    @ApiModelProperty("文件")
    private List<WfAttachmentDto> fileList;

    public WfBusinessDataDetailDto(String key, String text, Object value, List<WfAttachmentDto> fileList) {
        this.key = key;
        this.text = text;
        this.value = value == null ? "" : value;
        this.fileList = fileList;
    }
}
