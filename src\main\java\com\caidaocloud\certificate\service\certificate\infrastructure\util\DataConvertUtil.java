package com.caidaocloud.certificate.service.certificate.infrastructure.util;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import lombok.SneakyThrows;

public class DataConvertUtil {


    @SneakyThrows
    public static Map<String,Object> convert2map(Object data,Class<?> clazz){
        Map<String, Object> map = new HashMap<>();
        if (data == null) {
            return map;
        }

        // 获取 Data 类的所有字段
        Field[] fields = data.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true); // 确保可以访问私有字段
            Object value = field.get(data);
            // 如果字段的值实现了接口 A，执行特殊处理
            if (value instanceof PropertyValue) {
                map.put(field.getName(), ((PropertyValue) value).toText());
            } else {
                map.put(field.getName(), value);
            }
        }
        return map;
    }
}