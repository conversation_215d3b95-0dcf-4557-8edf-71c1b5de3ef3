package com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl;

import java.util.List;

import com.caidaocloud.certificate.service.certificate.domain.repository.IWorkplaceRepository;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.MdDataQuery;
import com.caidaocloud.masterdata.entity.emp.EmpInfoEntity;
import com.caidaocloud.masterdata.entity.org.WorkPlaceEntity;

import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2024/5/7
 */
@Repository
public class WorkplaceRepositoryImpl implements IWorkplaceRepository {
	@Override
	public List<WorkPlaceEntity> loadList(List<String> bids) {
		return MdDataQuery.identifier(WorkPlaceEntity.WORKPLACE_IDENTIFIER).queryInvisible().decrypt().specifyLanguage()
				.limit(bids.size(), 1)
				.filter(DataFilter.in("bid", bids), WorkPlaceEntity.class, System.currentTimeMillis())
				.getItems();
	}
}
