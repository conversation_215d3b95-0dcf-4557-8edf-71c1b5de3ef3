package com.caidaocloud.certificate.service.certificate.interfaces.vo;

import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateStatus;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 * 证书类型查询dto
 **/
@Data
@Slf4j
@ApiModel(description = "证书使用情况列表Vo")
public class CertificateUsageRecordPageVo {
    private String bid;
    @ApiModelProperty( "证书id")
    private String certificate;
    @ApiModelProperty( "证书名称")
    private String certificateTxt;
    @ApiModelProperty( "登记类别")
    private DictSimple type;
    private Long startDate;
    private Long endDate;
    private EmpSimple emp;
    @ApiModelProperty("证书登记状态")
    private CertificateStatus certificateStatus;
    @ApiModelProperty("证书状态")
    private EnumSimple certificateActiveStatus;


    public void display(){
        if (startDate==0) {
            startDate = null;
        }
        if (endDate!=null && endDate== DateUtil.MAX_TIMESTAMP) {
            endDate = null;
        }
    }
}
