package com.caidaocloud.certificate.service.certificate.interfaces.facade;

import com.caidaocloud.certificate.service.certificate.application.service.QualifiedPerService;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.QualifiedPerQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.SetTopSaveDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/22 13:36
 * 证书资格人员
 **/
@RestController
@RequestMapping("/api/certificate/v1/qualifiedPer")
@Api(value = "/api/certificate/v1/qualifiedPer", description = "证书资格人员", tags = "v0.1")
public class QualifiedPerController {
    @Autowired
    private QualifiedPerService qualifiedPerService;

    @ApiOperation("分页查询")
    @PostMapping("/pageList")
    public Result<PageResult<Map<String, Object>>> pageList(@RequestBody CeritificateAndEmpQueryDto queryDto, HttpServletRequest request) {
        String header = request.getHeader("Accept-Language");
        if(StringUtils.isNotEmpty(queryDto.getKeywords())){
            queryDto.setNameOrNo(queryDto.getKeywords());
        }
        //apiType 设置为1 非空
        queryDto.setApiType("1");
        if (StringUtils.isEmpty(queryDto.getEmpStatus())){
            queryDto.setEmpStatus("0");
        }

        return Result.ok(qualifiedPerService.pageList(queryDto,header));
    }
    @ApiOperation("设置证书表头")
    @PostMapping("/setTop")
    public Result setTop(@RequestBody SetTopSaveDto dto) {
        dto.setEmpId(UserContext.getUserId());
        return Result.ok(qualifiedPerService.saveSetTop(dto));
    }

    @ApiOperation("根据员工号展示对应的证书类型和证书")
    @GetMapping("/select")
    @ResponseBody
    public Result selectUseCertificate() {
        return Result.ok(qualifiedPerService.selectUseCertificate());

    }

    @PostMapping("/export")
    @ApiOperation("导出")
    public void export(@RequestBody CeritificateAndEmpQueryDto queryDto, HttpServletResponse response){
        HttpServletRequest request = WebUtil.getRequest();
        String header = request.getHeader("Accept-Language");

        if(StringUtils.isNotEmpty(queryDto.getKeywords())){
            queryDto.setNameOrNo(queryDto.getKeywords());
        }
        //apiType 设置为1 非空
        queryDto.setApiType("1");
        if (StringUtils.isEmpty(queryDto.getEmpStatus())){
            queryDto.setEmpStatus("0");
        }
        qualifiedPerService.export(queryDto, header, response);
    }

    /**
     * @deprecated  这里是为动态列做准备对后期修改 导出和列表都需要从这个表中引用数据
     * @return
     */
    @ApiOperation("设置对证书表头详情")
    @PostMapping("/getTopDatails")
    public Result getTopDatails() {
        return Result.ok(qualifiedPerService.getTopDatails(UserContext.getUserId()));
    }
}
