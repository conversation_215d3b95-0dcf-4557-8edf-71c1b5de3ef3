package com.caidaocloud.certificate.service.certificate.interfaces.vo.org;


import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeParent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
public class OrgVo {
    @ApiModelProperty("组织ID")
    private String bid;
    @ApiModelProperty("组织名称")
    private String name;
    @ApiModelProperty("组织简称多语言")
    private Map<String, Object> i18nName;
    @ApiModelProperty("组织全称")
    private String fullName;
    @ApiModelProperty("组织全称多语言")
    private Map<String, Object> i18nFullName;
    @ApiModelProperty("组织编码")
    private String code;
    @ApiModelProperty("上级组织")
    private TreeParent pid;
    @ApiModelProperty("组织类型")
    private DictSimple orgType;
    @ApiModelProperty("虚拟组织")
    private Boolean virtual;
    @ApiModelProperty("状态")
    private EnumSimple status;

    /**
     * 架构类型
     */
    @ApiModelProperty("组织架构类型类型")
    private DictSimple schemaType;

    /**
     * 成本中心ID
     */
    @ApiModelProperty("成本中心ID")
    private String costCenterId;

    /**
     * 成本中心名称
     */
    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    /**
     * 是否同步更新子组织[其他组织角色]
     * 同步更新子组织
     */
    @ApiModelProperty("同步更新子组织")
    private Boolean syncUpdate;

    /**
     * 是否维护其他架构类型汇报关系
     */
    @ApiModelProperty("维护其他架构类型汇报关系")
    private Boolean updateSchema;

    /**
     * 其他架构类型汇报关系
     */
    @ApiModelProperty("其他架构类型汇报关系")
    private List<OrgReportExtendDto> reportSchema;

    /**
     * 时间轴数据变更时间
     */
    @ApiModelProperty("时间轴数据变更时间")
    private long dataStartTime;
    @ApiModelProperty("责任人")
    private EmpSimple leaderEmp;
    @ApiModelProperty("责任人（岗位）id")
    private String leaderPost;
    @ApiModelProperty("责任人岗位名称")
    private String leaderPostTxt;
    @ApiModelProperty("责任人（组织）id")
    private String leaderOrganize;
    @ApiModelProperty("责任人（组织）名称")
    private String leaderOrganizeTxt;
    @ApiModelProperty("hrbp（岗位）")
    private String hrbpPost;
    @ApiModelProperty("hrbp（岗位）名称")
    private String hrbpPostTxt;
    @ApiModelProperty("其他组织角色信息组")
    private List<RoleInfoDto> otherRoles;
    @ApiModelProperty("其他组织角色")
    private List<CustomOrgRoleVo> customOrgRoles;
    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
