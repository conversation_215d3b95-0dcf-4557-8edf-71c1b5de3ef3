package com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateTypeDo;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateTypeRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateTypeQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeData;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/10 13:47
 * 证书管理
 **/
@Repository
public class CertificateTypeRepositoryImpl extends BaseRepositoryImpl<CertificateTypeDo> implements ICertificateTypeRepository {
    @Override
    public List<TreeData<CertificateTypeDo>> treeList(CertificateTypeQueryDto dto, String identifier) {
        if (StringUtils.isEmpty(dto.getName())){
          return DataQuery.identifier(identifier).tree(CertificateTypeDo.class);
        }
        DataFilter dataFilter = DataFilter.eq("name", dto.getName());
        return DataQuery.identifier(identifier).tree(CertificateTypeDo.class, System.currentTimeMillis(), dataFilter);
    }

    @Override
    public List<CertificateTypeDo> selectList(CertificateTypeQueryDto query, String identifier) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .eq("deleted", Boolean.FALSE.toString());
        if (null != query.getStatus()) {
            dataFilter = dataFilter.andEq("status", query.getStatus());
        }

        if (StringUtil.isNotEmpty(query.getName())) {
            dataFilter = dataFilter.and(DataFilter.regex("name", query.getName())
            );
        }
        if (StringUtil.isNotEmpty(query.getKeyWord())) {
            dataFilter = dataFilter.and(DataFilter.regex("name", query.getKeyWord()).orRegex("code", query.getKeyWord())
            );
        }
        if (StringUtil.isNotEmpty(query.getCode())) {
            dataFilter = dataFilter.and(DataFilter.regex("code", query.getCode())
            );
        }
        if (StringUtil.isNotEmpty(query.getPBid())) {//删除校验有用到
            dataFilter = dataFilter.and(DataFilter.eq("pBid", query.getPBid()));
        }
        if (StringUtil.isNotEmpty(query.getLevel())) {//列表可用
            dataFilter = dataFilter.and(DataFilter.regex("level", String.valueOf(query.getLevel())));
        }
        return DataQuery.identifier(identifier)
                .decrypt().specifyLanguage().queryInvisible().limit(5000, 1)
                .filter(dataFilter, CertificateTypeDo.class).getItems();
    }

    @Override
    public List<CertificateTypeDo> all() {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId());
        return DataQuery.identifier(CertificateTypeDo.IDENTIFIER)
                .decrypt().specifyLanguage().queryInvisible().limit(5000, 1)
                .filter(dataFilter, CertificateTypeDo.class).getItems();
    }

}
