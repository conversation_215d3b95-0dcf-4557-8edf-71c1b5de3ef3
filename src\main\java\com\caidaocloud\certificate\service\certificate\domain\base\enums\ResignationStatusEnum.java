package com.caidaocloud.certificate.service.certificate.domain.base.enums;

/**
 * 离职状态
 * <AUTHOR>
 * @date 2023/4/6
 */
public enum ResignationStatusEnum {
	// 待离职
	PENDING("待离职"),
	// 已离职
	LEFT("已离职");

	public final String text;

	ResignationStatusEnum(String text) {
		this.text = text;
	}

	public static ResignationStatusEnum getByName(String name) {
		if (name == null) {
			return null;
		}
		for (ResignationStatusEnum e : values()) {
			if (e.name().equals(name)) {
				return e;
			}
		}
		return null;
	}
}
