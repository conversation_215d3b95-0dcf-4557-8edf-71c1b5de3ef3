package com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateTypeDo;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateQueryDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Repository;
import org.springframework.util.function.SupplierUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/10 13:47
 * 证书
 **/
@Repository
public class CertificateRepositoryImpl extends BaseRepositoryImpl<CertificateDo> implements ICertificateRepository {

    @Override
    public List<CertificateDo> selectList(CertificateQueryDto basePage, String identifier) {
        DataFilter dataFilter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
                .andEq("deleted", Boolean.FALSE.toString());
        if (null != basePage.getStatus()) {
            dataFilter = dataFilter.andEq("status", basePage.getStatus());
        }

        if (StringUtil.isNotEmpty(basePage.getNameOrCode())) {
            dataFilter = dataFilter.and(DataFilter.regex("name", basePage.getNameOrCode())
                    .orRegex("code", basePage.getNameOrCode()));
        }
        if (StringUtil.isNotEmpty(basePage.getTypeBid())) {
            dataFilter = dataFilter.and(DataFilter.eq("typeBid", basePage.getTypeBid())

            );
        }
        if (StringUtil.isNotEmpty(basePage.getEmpId())) {
            dataFilter = dataFilter.and(DataFilter.eq("empId", basePage.getEmpId())

            );
        }
        return DataQuery.identifier(identifier)
                .decrypt().specifyLanguage().queryInvisible().limit(5000, 1)
                .filter(dataFilter, CertificateDo.class,  System.currentTimeMillis()).getItems();
    }

    @Override
    public PageResult<CertificateDo> sortPageList(CertificateQueryDto queryDto, String identifier) {
        // 如果没有指定typeBid，使用join查询关联证书类型
        if (StringUtil.isEmpty(queryDto.getTypeBid())) {
            return pageListWithJoin(queryDto, identifier);
        }

        // 原有的简单查询逻辑
        DataFilter dataFilter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
                .andEq("deleted", Boolean.FALSE.toString());
        if (null != queryDto.getStatus()) {
            dataFilter = dataFilter.andEq("status", queryDto.getStatus());
        }

        if (StringUtil.isNotEmpty(queryDto.getNameOrCode())) {
            dataFilter = dataFilter.and(DataFilter.regex("name", queryDto.getNameOrCode())
                    .orRegex("code", queryDto.getNameOrCode()));
        }
        if (StringUtil.isNotEmpty(queryDto.getTypeBid())) {
            dataFilter = dataFilter.and(DataFilter.eq("typeBid", queryDto.getTypeBid())

            );
        }
        return DataQuery.identifier(identifier)
                .decrypt().specifyLanguage().queryInvisible().limit(queryDto.getPageSize(), queryDto.getPageNo())
                .filter(dataFilter, CertificateDo.class, "sortNum asc,id desc", System.currentTimeMillis());
    }

    /**
     * 使用join查询关联证书类型的分页查询
     * 
     * @param queryDto   查询条件
     * @param identifier 标识符
     * @return 分页结果
     */
    private PageResult<CertificateDo> pageListWithJoin(CertificateQueryDto queryDto, String identifier) {
        // 构建证书模型的过滤条件
        DataFilter certificateFilter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
                .andEq("deleted", Boolean.FALSE.toString());
        if (null != queryDto.getStatus()) {
            certificateFilter = certificateFilter.andEq("status", queryDto.getStatus());
        }
        if (StringUtil.isNotEmpty(queryDto.getNameOrCode())) {
            certificateFilter = certificateFilter.and(DataFilter.regex("name", queryDto.getNameOrCode())
                    .orRegex("code", queryDto.getNameOrCode()));
        }

        // 构建2类证书类型模型的过滤条件
        DataFilter level2TypeFilter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
                .andEq("deleted", Boolean.FALSE.toString())
                .andEq("level", "2")
                .andEq("status", "0"); // 只查询启用状态的证书类型

        // 构建1类证书类型模型的过滤条件
        DataFilter level1TypeFilter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
                .andEq("deleted", Boolean.FALSE.toString())
                .andEq("level", "1")
                .andEq("status", "0"); // 只查询启用状态的证书类型

        // 创建模型信息
        DataJoin.ModelInfo level1TypeModel = DataJoin.ModelInfo.model(CertificateTypeDo.IDENTIFIER, Lists.list(),
                level1TypeFilter).orderBy("sort_num::int8 asc").orderBy("id desc");
        DataJoin.ModelInfo level2TypeModel = DataJoin.ModelInfo.model(CertificateTypeDo.IDENTIFIER, Lists.list(),
                level2TypeFilter).orderBy("sort_num::int8 asc").orderBy("id desc");
        DataJoin.ModelInfo certificateModel = DataJoin.ModelInfo.model(identifier, Lists.list("bid","name","code","sort_num","status","type_bid","i18n_name","emp_id","pro_bid"), certificateFilter)
                .orderBy("sort_num::int8 asc").orderBy("id desc");

        // 创建join查询：证书 -> 2类证书类型 -> 1类证书类型
        DataJoin join = DataJoin.joinModels(level1TypeModel, level2TypeModel,
                DataJoin.JoinInfo.joinInfo(Lists.list(
                        DataJoin.JoinPropertyInfo.joinProperty(level1TypeModel.getIdentifier(),
                                level2TypeModel.getIdentifier(), "bid", "pBid"))));

        // 添加1类证书类型的关联
        join.joinModel(certificateModel,
                DataJoin.JoinInfo.joinInfo(Lists.list(
                        DataJoin.JoinPropertyInfo.joinProperty(level2TypeModel,
                                certificateModel, "bid", "typeBid"))));

        // 设置分页和排序
        join.limit(queryDto.getPageSize(), queryDto.getPageNo());

        // 执行join查询
        PageResult<Triple<CertificateDo, CertificateDo, CertificateDo>> result = join.join(CertificateDo.class,
                System.currentTimeMillis());

        // 提取证书数据并按sortNum排序
        List<CertificateDo> certificates = result.getItems().stream()
                .map(Triple::getRight)
                .collect(Collectors.toList());

        return new PageResult<>(certificates, result.getPageNo(), result.getPageSize(), result.getTotal());
    }

    @Override
    public List<CertificateDo> selectListByIds(List<String> bids) {
        return DataQuery.identifier("entity.certificate.certificate")
                .decrypt().specifyLanguage().queryInvisible()
                .limit(bids.size(), 1)
                // TODO: 2025/7/9 排序
                .filter(DataFilter.in("bid", bids), CertificateDo.class, "sortNum asc,id desc",
                        System.currentTimeMillis())
                .getItems();
    }

    @Override
    @Deprecated
    public List<CertificateDo> all() {
        DataFilter dataFilter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId());
        return DataQuery.identifier(CertificateDo.IDENTIFIER)
                .decrypt().specifyLanguage().queryInvisible().limit(5000, 1)
                .filter(dataFilter, CertificateDo.class, "sortNum asc,id desc", System.currentTimeMillis()).getItems();
    }

}
