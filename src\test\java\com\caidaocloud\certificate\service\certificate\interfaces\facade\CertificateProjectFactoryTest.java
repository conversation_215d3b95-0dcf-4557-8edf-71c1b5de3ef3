package com.caidaocloud.certificate.service.certificate.interfaces.facade;

/**
 *
 * <AUTHOR>
 * @date 2024/5/14
 */

import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

import com.caidaocloud.certificate.CertificateApplication;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.domain.enums.CertificateStatus;
import com.caidaocloud.certificate.service.certificate.domain.factory.CertificateProjectFactory;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICeritificateAndEmpRepository;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateProjectRepository;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateUsageRecordRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateUsageRecordSaveDto;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateUsageRecordDo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.googlecode.totallylazy.Lists;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


@SpringBootTest(classes = CertificateApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class CertificateProjectFactoryTest {

	@Autowired
	private CertificateProjectFactory certificateProjectFactory;

	@MockBean
	private ICertificateProjectRepository certificateProjectRepository;
	@MockBean
	private ICertificateUsageRecordRepository certificateUsageRecordRepository;
	@MockBean
	private ICeritificateAndEmpRepository ceritificateAndEmpRepository;

	@Before
	public void before(){
		Mockito.doReturn(null).when(certificateUsageRecordRepository).save(Mockito.any());
		when(certificateUsageRecordRepository.selectUsageRecordByCertificate(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new PageResult<>(Lists.list()));
		CeritificateAndEmpDo data = new CeritificateAndEmpDo();
		data.setEmpId("123");
		when(ceritificateAndEmpRepository.selectById(Mockito.any(), CeritificateAndEmpDo.IDENTIFIER)).thenReturn(data);
	}


	@Test
	public void testCreateUsageRecordWithNullStartDateAndBeforeEndDate() {
		// Arrange
		Long endDate =LocalDate.of(2024, 5, 1).atStartOfDay().toInstant(OffsetDateTime.now().getOffset()).toEpochMilli(); // Example end date in milliseconds
		String empId = "12345";
		String type = "exampleType";

		CertificateUsageRecordSaveDto dto = new CertificateUsageRecordSaveDto();
		dto.setEndDate(endDate);
		dto.setType(type);

		// Act
		CertificateUsageRecordDo result = certificateProjectFactory.createUsageRecord(dto, empId);

		result.save();
		assertEquals(CertificateStatus.UNLOCKED, result.getCertificateStatus());
	}

	@Test
	public void testCreateUsageRecordWithNullStartDateAndAfterEndDate() {
		// Arrange
		Long endDate =LocalDate.of(2024, 5, 31).atStartOfDay().toInstant(OffsetDateTime.now().getOffset()).toEpochMilli(); // Example end date in milliseconds
		String empId = "12345";
		String type = "exampleType";

		CertificateUsageRecordSaveDto dto = new CertificateUsageRecordSaveDto();
		dto.setEndDate(endDate);
		dto.setType(type);

		// Act
		CertificateUsageRecordDo result = certificateProjectFactory.createUsageRecord(dto, empId);


		result.save();
		assertEquals(CertificateStatus.LOCKED, result.getCertificateStatus());
	}

	@Test
	public void testCreateUsageRecordWithFutureDateRange() {
		// Arrange
		Long endDate =LocalDate.of(2024, 7, 31).atStartOfDay().toInstant(OffsetDateTime.now().getOffset()).toEpochMilli(); // Example end date in milliseconds
		Long start =LocalDate.of(2024, 6, 30).atStartOfDay().toInstant(OffsetDateTime.now().getOffset()).toEpochMilli(); // Example end date in milliseconds
		String empId = "12345";
		String type = "exampleType";

		CertificateUsageRecordSaveDto dto = new CertificateUsageRecordSaveDto();
		dto.setEndDate(endDate);
		dto.setStartDate(start);
		dto.setType(type);

		// Act
		CertificateUsageRecordDo result = certificateProjectFactory.createUsageRecord(dto, empId);

		result.save();
		assertEquals(CertificateStatus.UNLOCKED, result.getCertificateStatus());
	}

	@Test
	public void testCreateUsageRecordWithNullDate() {
		// Arrange
		String empId = "12345";
		String type = "exampleType";

		CertificateUsageRecordSaveDto dto = new CertificateUsageRecordSaveDto();
		// dto.setStartDate(startDate);
		dto.setType(type);
		// dto.setCertificate("123");

		// Act
		CertificateUsageRecordDo result = certificateProjectFactory.createUsageRecord(dto, empId);



		result.save();
		assertEquals(CertificateStatus.LOCKED, result.getCertificateStatus());
	}
}
