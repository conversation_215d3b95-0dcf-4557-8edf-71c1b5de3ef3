package com.caidaocloud.certificate.service.certificate.domain.enums;

import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;

public enum CertificateStatus {

    LOCKED("已锁定"),
    UNLOCKED("已解锁");

    private String description;

    CertificateStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 获取证书状态对应的描述
     * @param statusEnum 状态枚举值
     * @return 描述字符串
     */
    public static String getDescriptionByEnum(CertificateStatus statusEnum) {
        return statusEnum.getDescription();
    }

    /**
     * 根据描述字符串获取对应的状态枚举值
     * @param description 描述字符串
     * @return 状态枚举值，如果未找到匹配项，则返回 null
     */
    public static CertificateStatus getByDescription(String description) {
        for (CertificateStatus status : values()) {
            if (status.getDescription().equals(description)) {
                return status;
            }
        }
        return null;
    }


    public static  CertificateStatus determineProjectStatus(Long startDate,Long endDate) {
        long now = System.currentTimeMillis();
        long start = startDate == null ? 0 : startDate;
        long end = endDate == null ? DateUtil.MAX_TIMESTAMP : endDate;
        if (now < start) {
            return CertificateStatus.UNLOCKED;
        }
        else if (now < end) {
            return CertificateStatus.LOCKED;
        }
        else {
            return CertificateStatus.UNLOCKED;
        }
    }
}
