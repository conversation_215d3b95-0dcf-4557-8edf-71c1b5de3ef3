package com.caidaocloud.certificate.service.certificate.infrastructure.util;

import org.junit.Test;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.LocalDateTime;

import static org.junit.Assert.assertEquals;

public class DateUtilTest {

    @Test
    public void testGetTimestampOfDayStartAtMidnight() {
        // Given
        long timestamp = 1721145600000L; // An example timestamp
        long expectedTimestamp = 1721145600000L;

        // When
        long resultTimestamp = DateUtil.getTimestampOfDayStart(timestamp);

        // Then
        assertEquals("The timestamp should be at the start of the day", expectedTimestamp, resultTimestamp);
    }

    @Test
    public void testGetTimestampOfDayStartNotAtMidnight() {
        // Given
        long timestamp = 1721197402345L; // An example timestamp not at midnight
        long expectedTimestamp = 1721145600000L;

        // When
        long resultTimestamp = DateUtil.getTimestampOfDayStart(timestamp);

        // Then
        assertEquals("The timestamp should be adjusted to the start of the day", expectedTimestamp, resultTimestamp);
    }

    @Test
    public void testGetTimestampOfDayStartAtDayChange() {
        // Given
        long timestamp = 1721231999000L; // An example timestamp exactly at the start of a new day
        long expectedTimestamp = 1721145600000L;

        // When
        long resultTimestamp = DateUtil.getTimestampOfDayStart(timestamp);

        // Then
        assertEquals("The timestamp should remain the same as it's already at the start of the day", expectedTimestamp, resultTimestamp);
    }

}
