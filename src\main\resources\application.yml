spring:
 profiles:
  active: dev


msg:
 middleware:
  type: rabbitmq

rabbitmq:
 topics:
  - topic: FORM_PUBLISH
    exchange: caidao.hrpaas
    routingKey: caidao.hrpaas.form.publish
    queue: caidaocloud.certificate.page.detail.change
    exchangeType: DIRECT
    tenantIsolated: false
    consumersCount: 1
  - topic: Certificate_Acquired
    exchange: caidao.certificate
    routingKey: caidao.certificate.acquired
    queue: caidao.certificate.acquired.queue
    exchangeType: DIRECT
    tenantIsolated: false
    consumersCount: 1
i18n:
  resource:
    path: i18n/certificate/messages

authScope:
  /api/certificate/v1/emp/listData : entity.certificate.certificateAndEmp
  /api/certificate/v1/emp/list : entity.certificate.certificateAndEmp
  /api/certificate/v1/qualifiedPer/pageList : entity.certificate.certificateAndEmp
  /api/certificate/v1/project/page : entity.certificate.CertificateProject
  /api/certificate/v1/project/detail : entity.certificate.CertificateProject