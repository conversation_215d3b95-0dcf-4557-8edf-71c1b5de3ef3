package com.caidaocloud.certificate.service.certificate.domain.repository;

import com.caidaocloud.certificate.service.certificate.domain.base.repository.BaseRepository;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateTypeDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateTypeQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeData;

import java.util.List;

public interface ICertificateTypeRepository extends BaseRepository<CertificateTypeDo> {
    /**
     * 获取树形列表
     * @param dto
     * @param identifier
     * @return
     */
    List<TreeData<CertificateTypeDo>> treeList(CertificateTypeQueryDto dto, String identifier);

    /**
     * 列表查询
     * @param basePage
     * @param identifier
     * @return
     */
    List<CertificateTypeDo> selectList(CertificateTypeQueryDto basePage, String identifier);

    List<CertificateTypeDo> all();
}
