package com.caidaocloud.certificate.service.certificate.domain.base.util;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2021/11/24
 */
@Slf4j
public final class UserContext {
    private final static ThreadLocal<UserInfo> USER_CONTEXT = new ThreadLocal<>();

    public static void setCurrentUser(UserInfo user) {
        USER_CONTEXT.remove();
        if (user != null) {
            USER_CONTEXT.set(user);
        }
    }

    /**
     * 获取用户信息
     * @return
     */
    public static UserInfo getCurrentUser() {
        return USER_CONTEXT.get();
    }

    public static UserInfo getContextUser() {
        UserInfo user = USER_CONTEXT.get();
        user = null == user ? SpringUtil.getBean(ISessionService.class).getUserInfo() : user;
        return user;
    }

    /**
     * 清除用户信息
     */
    public static void remove() {
        USER_CONTEXT.remove();
    }

    public static UserInfo preCheckUser() {
        UserInfo user = getCurrentUser();
        if(null != user){
            return user;
        }

        UserInfo userInfo = null;
        SecurityUserInfo ui = SecurityUserUtil.getSecurityUserInfo();
        if(null != ui){
            userInfo = new UserInfo();
            userInfo.setTenantId(ui.getTenantId());
            userInfo.setUserid(null != ui.getUserId() ? ui.getUserId().intValue() : null);
            userInfo.doSetUserId(ui.getUserId());
            userInfo.setStaffId(ui.getEmpId());
            return userInfo;
        }

        try {
            userInfo = SpringUtil.getBean(ISessionService.class).getUserInfo();
        } catch (Exception e){
            log.warn("UserContext,get user by session err.");
        }

        PreCheck.preCheckArgument(checkUser(userInfo), "登录失效");
        return userInfo;
    }

    public static String getUserId() {
        UserInfo userInfo = UserContext.preCheckUser();
        return null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
    }

    public static String getTenantId() {
        UserInfo userInfo = UserContext.preCheckUser();
        return null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
    }

    public static Long getStaffId() {
        UserInfo userInfo = UserContext.preCheckUser();
        return null == userInfo || null == userInfo.getStaffId() ? null : userInfo.getStaffId();
    }

    public static boolean checkUser(UserInfo userInfo) {
        return null == userInfo || null == userInfo.getUserId();
    }

    public static void preCheckUser(UserInfo userInfo) {
        PreCheck.preCheckArgument(checkUser(userInfo), "登录失效");
    }

}
