package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/15 13:35
 *  证书人员管理列表展示类
 **/
@Data
public class CeritificateAndEmpQueryDto extends BasePage {
    private String keywords;
    /**
     * 筛选条件
     */
    @ApiModelProperty("工号/姓名")
    private String nameOrNo;
    @ApiModelProperty("员工状态")
    private String empStatus;
    @ApiModelProperty("员工任职组织")
    private String organize;
    @ApiModelProperty("员工任职岗位")
    private String post;
    @ApiModelProperty("员工任职组织")
    private String organizeId;
    @ApiModelProperty("员工任职岗位")
    private String postId;
    @ApiModelProperty("员工工龄")
    private Double workAge;
    @ApiModelProperty("员工任职职务")
    private String job;
    @ApiModelProperty("员工职级")
    private String jobLevel;
    @ApiModelProperty("员工任职职务")
    private String jobId;
    @ApiModelProperty("员工职级")
    private String jobLevelId;
    @ApiModelProperty("注册日期")
    private String registerTime;
    @ApiModelProperty("失效时间")
    private String expireTime;
    @ApiModelProperty("社保缴纳地")
    private Address socialSecurity;
    private String shebaodi;
    @ApiModelProperty("签发地")
    private String issueAt;
    @ApiModelProperty("入省备案")
    private String accessProvince;
    @ApiModelProperty("持有证书数量")
    private Integer certifiCateNums;
    @ApiModelProperty("证书类别")
    private String typeName;
    @ApiModelProperty("证书名称")
    private String ceritifiCateName;
    private String certificateIds;
    @ApiModelProperty("证书子类型")
    private String typeSubName;
    @ApiModelProperty("证书类别")
    private String typeId;
    @ApiModelProperty("证书子类型")
    private String typeSubId;
    @ApiModelProperty("是否注册")
    private String isRegister;


    @ApiModelProperty("接口类型判断 1为资格人员接口2为证书登记一览接口")
    private String ApiType;
    @ApiModelProperty("证书子类型bid")
    private String typeSubBid;
    @ApiModelProperty("员工id")
    private String empId;
    @ApiModelProperty("证书bid")
    private String ceritifiCateBid;;
    @ApiModelProperty("证书类型bid")
    private String typeBid;;
    @ApiModelProperty("证书状态")// 有效无效
    private String ceritificateStatus;
    @ApiModelProperty("证书使用状态")// 使用/使用中
    private String useStatus;
    @ApiModelProperty("项目bid")
    private String proBid;
    @ApiModelProperty("bid")
    private String bid;
    @ApiModelProperty("员工id")
    private List<String> empIds;
    @ApiModelProperty("证书id集合")
    private List<String> bids;


    @ApiModelProperty("是否显示仅有证书的员工")
    private boolean filterAvailable;

    @ApiModelProperty("基准岗位id")
    private String benchPost;

    @ApiModelProperty("过滤人员证书数据")
    private List<String> excludeBid;
}
