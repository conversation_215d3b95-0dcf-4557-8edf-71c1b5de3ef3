package com.caidaocloud.certificate.service.certificate.application.feign;

import java.util.List;

import com.caidaocloud.certificate.service.certificate.application.dto.EmpInfoDto;
import com.caidaocloud.certificate.service.certificate.application.dto.EmpSearchDto;
import com.caidaocloud.masterdata.dto.EmpWorkInfoVo;
import com.caidaocloud.masterdata.entity.emp.PrivateInfoEntity;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 主数据2.0接口
 **/
@FeignClient(value = "caidaocloud-masterdata-service", path = "/api/masterdata/v2/emp", fallback = MasterdataFeignFallback.class
        , configuration = FeignConfiguration.class, contextId = "masterdataFeignV2")
public interface MasterDataEmpInfoFeign {

    @PostMapping("/empInfo/list")
    Result<List<EmpInfoDto>> loadEmpInfoList(@RequestBody EmpSearchDto dto);

    @GetMapping("workInfo")
    Result<EmpWorkInfoVo> loadEmpWorkInfo(@RequestParam("empId") String empId, @RequestParam("datetime") Long datetime);

    @GetMapping("privateInfo")
    Result<PrivateInfoEntity> loadEmpPrivateInfo(@RequestParam("empId") String empId);

    @GetMapping("/privateInfo")
    Result<PrivateInfoEntity> getEmpPrivateInfo(@RequestParam("empId") String empId, @RequestParam("isFilter") Boolean isFilter);
}