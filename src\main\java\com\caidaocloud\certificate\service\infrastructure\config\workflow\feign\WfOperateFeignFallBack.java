package com.caidaocloud.certificate.service.infrastructure.config.workflow.feign;


import com.caidaocloud.certificate.service.infrastructure.config.workflow.dto.WfTaskApproveDTO;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class WfOperateFeignFallBack implements IWfOperateFeignClient {
    @Override
    public Result approveTask(WfTaskApproveDTO wfApproveTaskDTO) {
        return Result.fail();
    }

//    @Override
//    public Result backTask(WfTaskBackDTO wfTaskBackDTO) {
//        return Result.fail();
//    }
//
//    @Override
//    public Result<Boolean> checkDefEnabled(String funCode) {
//        return Result.fail();
//    }
//
//    @Override
//    public Result urgeTask(WfTaskUrgeDTO wfTaskUrgeDTO) {
//        return Result.fail();
//    }
//
//    @Override
//    public Result<List<WfProcessRecordDto>> getRecord(String businessKey) {
//        return Result.fail();
//    }
}
