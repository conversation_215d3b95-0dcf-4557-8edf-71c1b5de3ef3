package com.caidaocloud.certificate.service.certificate.application.feign;


import com.caidaocloud.certificate.service.certificate.interfaces.vo.org.OrgVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@Component
public class HrFeignFallback implements IHrWorkFeign {

	@Override
	public Result<OrgVo> getDetail(String bid, Long dataTime) {
		return null;
	}

	@Override
	public Result<EmpWorkInfoVo> getEmpWorkInfo(String empId, Long dataTime) {
		return null;
	}

	@Override
	public Result<Map> getEmpInfoSimple(String empId, Long dataTime) {
		return null;
	}

	@Override
	public Result<List<Map>> listEmpInfoSimple(List<String> empIds, Long dataTime) {
		return null;
	}

	@Override
	public Result getEmpCurrentContract(String empId) {
		return null;
	}


}
