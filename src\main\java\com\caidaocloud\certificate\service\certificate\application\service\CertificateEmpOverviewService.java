package com.caidaocloud.certificate.service.certificate.application.service;

import javax.annotation.Resource;

import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateEmpOverviewDo;
import com.caidaocloud.certificate.service.certificate.domain.repository.IEmpInfoRepository;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.masterdata.entity.emp.EmpInfoEntity;
import com.caidaocloud.masterdata.entity.emp.JobOverviewEntity;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Sequences;

import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2024/5/19
 */
@Service
@Deprecated
public class CertificateEmpOverviewService {
	@Resource
	private IEmpInfoRepository empInfoRepository;

	public void saveOverview(String empId) {
		createOverview(empId).save();
	}

	private CertificateEmpOverviewDo createOverview(String empId) {
		CertificateEmpOverviewDo overview = new CertificateEmpOverviewDo();
		// 员工信息
		EmpInfoEntity entity = empInfoRepository.selectById(empId, EmpInfoEntity.EMP_IDENTIFIER);
		BeanUtil.copyWithNoValue(entity, overview);
		overview.setEmp(FastjsonUtil.convertObject(entity, EmpSimple.class));

		// 证书数量
		long time = System.currentTimeMillis();
		long activeCertificate = Sequences.sequence(CeritificateAndEmpDo.lisyByEmpId(empId))
				.filter(ceritificateAndEmpDo -> ceritificateAndEmpDo.checkActive(time)).stream().count();
		overview.setCertificateNum((int) activeCertificate);

		// TODO: 2024/5/19 首次工作日
		return overview;
	}
}
