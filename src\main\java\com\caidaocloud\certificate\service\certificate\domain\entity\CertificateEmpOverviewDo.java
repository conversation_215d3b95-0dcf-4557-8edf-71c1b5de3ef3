package com.caidaocloud.certificate.service.certificate.domain.entity;

import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateEmpOverviewRepository;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/5/17
 */
@Data
public class CertificateEmpOverviewDo extends DataSimple {
	private EmpSimple emp;
	private String organize;
	private String post;
	private String empStatus;
	private String job;
	private JobGradeRange jobGrade;
	private Integer certificateNum;
	private Long firstWorkDate;
	private Address socialSecurity;

	public void save(){
		setUpdateTime(System.currentTimeMillis());
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		if (getCreateTime() == 0L) {
			setCreateTime(getUpdateTime());
			setCreateBy(getUpdateBy());
		}
		SpringUtil.getBean(ICertificateEmpOverviewRepository.class).save();
	}
}
