package com.caidaocloud.certificate.service.certificate.domain.enums;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

/**
 * 证书使用状态枚举
 */
public enum CertificateUseStatusEnum {
    
    /**
     * 可使用
     */
    AVAILABLE("0", "未使用"),
    
    /**
     * 使用中
     */
    IN_USE("1", "使用中");
    
    private final String value;
    private final String text;
    
    CertificateUseStatusEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }
    
    public String getValue() {
        return value;
    }
    
    public String getText() {
        return text;
    }
    
    /**
     * 创建EnumSimple对象
     */
    public EnumSimple toEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(this.value);
        enumSimple.setText(this.text);
        return enumSimple;
    }
    
    /**
     * 根据值获取枚举
     */
    public static CertificateUseStatusEnum fromValue(String value) {
        for (CertificateUseStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return AVAILABLE; // 默认返回可使用
    }
}
