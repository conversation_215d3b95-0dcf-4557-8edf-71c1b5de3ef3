server:
 port: 8080

spring:
 rabbitmq:
  host: **************
  port: 5672
  username: admin
  password: myrabbitmq
  virtual-host: /caidaocloud2
  listener:
   simple:
    acknowledge-mode: auto
    prefetch: 1
 redis:
  host: **************
  port: 6379
  password: myredis
  database: 1
 cloud:
  nacos:
   discovery:
    server-addr: **************:8848
    namespace: cd2
 application:
  name: caidaocloud-certificate-service

nacos:
 config:
  type: yaml
  server-addr: **************:8848
  data-id: caidaocloud-certificate-service-config
  auto-refresh: true
  group: DEFAULT_GROUP
  namespace: cd2
  bootstrap:
   enable: true
   log:
    enable: true

