2024-12-17 16:11:44.427 [main] INFO  c.c.config.ErrorLogRollingPolicy - ErrorLogRollingPolicy init ........
2024-12-17 16:11:45.445 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-12-17 16:11:45.450 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-12-17 16:11:47.347 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='**************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-certificate-service-config', dataIds='null', group='DEFAULT_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-12-17 16:11:47.883 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-certificate-service-config, group is : DEFAULT_GROUP
2024-12-17 16:11:51.480 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-17 16:11:51.487 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-12-17 16:11:51.593 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 82ms. Found 0 repository interfaces.
2024-12-17 16:11:55.992 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-12-17 16:11:55.996 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-12-17 16:11:58.849 [main] INFO  org.reflections.Reflections - Reflections took 82 ms to scan 1 urls, producing 9 keys and 30 values 
2024-12-17 16:12:00.044 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:12:00.045 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:12:00.057 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@af4421ab
2024-12-17 16:12:00.092 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:12:00.507 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:12:00.519 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:12:00.734 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:12:00.737 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[**************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:**************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@ce548ffb
2024-12-17 16:12:01.059 [main] ERROR c.c.c.s.i.c.w.WfFunctionConfig - registerFunction Exception,{}
feign.FeignException: status 500 reading FormFeignClient#getFormDefByCode(String)
	at feign.FeignException.errorStatus(FeignException.java:78)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:93)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:149)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:78)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:103)
	at com.sun.proxy.$Proxy155.getFormDefByCode(Unknown Source)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.doInitWfRegister(WfFunctionConfig.java:89)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at <unknown class>.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:12:01.060 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-12-17 16:12:01.068 [main] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=null, canAlarm = true
2024-12-17 16:12:01.094 [main] INFO  com.caidaocloud.config.MailConfig - ------------------------ERROR log email sendHtmlMail fail,From address must not be null
java.lang.IllegalArgumentException: From address must not be null
	at org.springframework.util.Assert.notNull(Assert.java:198)
	at org.springframework.mail.javamail.MimeMessageHelper.setFrom(MimeMessageHelper.java:546)
	at com.caidaocloud.config.MailConfig.sendErrLogHtmlMail(MailConfig.java:83)
	at com.caidaocloud.config.MailConfig$$FastClassBySpringCGLIB$$4f67a738.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:684)
	at com.caidaocloud.config.MailConfig$$EnhancerBySpringCGLIB$$fbdb2e40.sendErrLogHtmlMail(<generated>)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:35)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:13)
	at ch.qos.logback.core.AppenderBase.doAppend(AppenderBase.java:82)
	at ch.qos.logback.core.spi.AppenderAttachableImpl.appendLoopOnAppenders(AppenderAttachableImpl.java:51)
	at ch.qos.logback.classic.Logger.appendLoopOnAppenders(Logger.java:270)
	at ch.qos.logback.classic.Logger.callAppenders(Logger.java:257)
	at ch.qos.logback.classic.Logger.buildLoggingEventAndAppend(Logger.java:421)
	at ch.qos.logback.classic.Logger.filterAndLog_0_Or3Plus(Logger.java:383)
	at ch.qos.logback.classic.Logger.error(Logger.java:538)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:69)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at <unknown class>.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:12:01.524 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:12:02.774 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:12:02.775 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:12:05.105 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-12-17 16:12:05.409 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for **************/**************:6379
2024-12-17 16:12:05.697 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for **************/**************:6379
2024-12-17 16:12:07.455 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateAndEmpStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@878cb9ab[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAndEmpStatusTask#execute]
2024-12-17 16:12:07.456 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateAuthFieldTaskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@a5cf1761[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAuthFieldTask#execute]
2024-12-17 16:12:07.456 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateExpireJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@679e3450[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateExpireTask#notice]
2024-12-17 16:12:07.456 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@9b9d0528[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#execute]
2024-12-17 16:12:07.457 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateProjectStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6724cc8b[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#projectStatusHandler]
2024-12-17 16:12:07.800 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-12-17 16:12:07.836 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-12-17 16:12:07.862 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-12-17 16:12:07.865 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-12-17 16:12:07.945 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-12-17 16:12:08.507 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_1
2024-12-17 16:12:08.534 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2024-12-17 16:12:08.537 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-12-17 16:12:08.562 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_2
2024-12-17 16:12:08.565 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-12-17 16:12:08.579 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_1
2024-12-17 16:12:08.584 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_3
2024-12-17 16:12:08.703 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_2
2024-12-17 16:12:08.706 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_1
2024-12-17 16:12:08.713 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_1
2024-12-17 16:12:08.723 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_4
2024-12-17 16:12:08.732 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_3
2024-12-17 16:12:08.734 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2024-12-17 16:12:08.736 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingPOST_1
2024-12-17 16:12:08.745 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_5
2024-12-17 16:12:08.749 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2024-12-17 16:12:08.752 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_1
2024-12-17 16:12:08.757 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2024-12-17 16:12:08.767 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageListUsingPOST_1
2024-12-17 16:12:08.778 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_2
2024-12-17 16:12:08.839 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2024-12-17 16:14:49.585 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:14:49.618 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-masterdata-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-masterdata-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:14:49.621 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:14:49.674 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:14:49.676 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-masterdata-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-masterdata-service,current list of Servers=[************:20008],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:20008;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@74b87636
2024-12-17 16:14:50.635 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:15:11.947 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-12-17 16:15:11.949 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-12-17 16:15:11.952 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-12-17 16:15:11.956 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-12-17 16:15:11.957 [Thread-26] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-12-17 16:15:12.670 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-12-17 16:15:12.693 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-certificate-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-12-17 16:15:12.696 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-12-17 16:15:12.697 [Thread-42] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-12-17 16:15:12.699 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-12-17 16:15:12.700 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-12-17 16:15:12.701 [Thread-39] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-12-17 16:15:12.841 [Thread-42] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-12-17 16:16:21.185 [main] INFO  c.c.config.ErrorLogRollingPolicy - ErrorLogRollingPolicy init ........
2024-12-17 16:16:22.910 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-12-17 16:16:22.916 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-12-17 16:16:25.548 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='**************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-certificate-service-config', dataIds='null', group='DEFAULT_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-12-17 16:16:27.638 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-certificate-service-config, group is : DEFAULT_GROUP
2024-12-17 16:16:30.742 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-17 16:16:30.752 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-12-17 16:16:30.893 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 105ms. Found 0 repository interfaces.
2024-12-17 16:16:33.513 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-12-17 16:16:33.519 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-12-17 16:16:36.239 [main] INFO  org.reflections.Reflections - Reflections took 86 ms to scan 1 urls, producing 9 keys and 30 values 
2024-12-17 16:16:37.558 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:16:37.559 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:16:37.574 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@ea402fde
2024-12-17 16:16:37.609 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:16:37.984 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:16:37.992 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:16:38.193 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:16:38.195 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[**************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:**************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@44fac0f9
2024-12-17 16:16:38.544 [main] ERROR c.c.c.s.i.c.w.WfFunctionConfig - registerFunction Exception,{}
feign.FeignException: status 500 reading FormFeignClient#getFormDefByCode(String)
	at feign.FeignException.errorStatus(FeignException.java:78)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:93)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:149)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:78)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:103)
	at com.sun.proxy.$Proxy155.getFormDefByCode(Unknown Source)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.doInitWfRegister(WfFunctionConfig.java:89)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at <unknown class>.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:16:38.545 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-12-17 16:16:38.554 [main] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=null, canAlarm = true
2024-12-17 16:16:38.585 [main] INFO  com.caidaocloud.config.MailConfig - ------------------------ERROR log email sendHtmlMail fail,From address must not be null
java.lang.IllegalArgumentException: From address must not be null
	at org.springframework.util.Assert.notNull(Assert.java:198)
	at org.springframework.mail.javamail.MimeMessageHelper.setFrom(MimeMessageHelper.java:546)
	at com.caidaocloud.config.MailConfig.sendErrLogHtmlMail(MailConfig.java:83)
	at com.caidaocloud.config.MailConfig$$FastClassBySpringCGLIB$$4f67a738.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:684)
	at com.caidaocloud.config.MailConfig$$EnhancerBySpringCGLIB$$a7c53c06.sendErrLogHtmlMail(<generated>)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:35)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:13)
	at ch.qos.logback.core.AppenderBase.doAppend(AppenderBase.java:82)
	at ch.qos.logback.core.spi.AppenderAttachableImpl.appendLoopOnAppenders(AppenderAttachableImpl.java:51)
	at ch.qos.logback.classic.Logger.appendLoopOnAppenders(Logger.java:270)
	at ch.qos.logback.classic.Logger.callAppenders(Logger.java:257)
	at ch.qos.logback.classic.Logger.buildLoggingEventAndAppend(Logger.java:421)
	at ch.qos.logback.classic.Logger.filterAndLog_0_Or3Plus(Logger.java:383)
	at ch.qos.logback.classic.Logger.error(Logger.java:538)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:69)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at <unknown class>.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:16:39.010 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:16:40.430 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:16:40.431 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:16:42.612 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-12-17 16:16:42.937 [redisson-netty-5-13] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for **************/**************:6379
2024-12-17 16:16:43.310 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for **************/**************:6379
2024-12-17 16:16:45.097 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateAndEmpStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@816212f7[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAndEmpStatusTask#execute]
2024-12-17 16:16:45.097 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateAuthFieldTaskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@e6bc2fd9[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAuthFieldTask#execute]
2024-12-17 16:16:45.097 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateExpireJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4443033c[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateExpireTask#notice]
2024-12-17 16:16:45.097 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@38eb1864[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#execute]
2024-12-17 16:16:45.097 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateProjectStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@618d556c[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#projectStatusHandler]
2024-12-17 16:16:45.451 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-12-17 16:16:45.480 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-12-17 16:16:45.504 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-12-17 16:16:45.512 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-12-17 16:16:45.584 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-12-17 16:16:46.142 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_1
2024-12-17 16:16:46.169 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2024-12-17 16:16:46.172 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-12-17 16:16:46.199 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_2
2024-12-17 16:16:46.203 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-12-17 16:16:46.216 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_1
2024-12-17 16:16:46.227 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_3
2024-12-17 16:16:46.344 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_2
2024-12-17 16:16:46.350 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_1
2024-12-17 16:16:46.355 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_1
2024-12-17 16:16:46.365 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_4
2024-12-17 16:16:46.375 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_3
2024-12-17 16:16:46.379 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2024-12-17 16:16:46.383 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingPOST_1
2024-12-17 16:16:46.393 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_5
2024-12-17 16:16:46.398 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2024-12-17 16:16:46.401 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_1
2024-12-17 16:16:46.404 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2024-12-17 16:16:46.416 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageListUsingPOST_1
2024-12-17 16:16:46.433 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_2
2024-12-17 16:16:46.501 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2024-12-17 16:17:13.725 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:17:13.819 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-masterdata-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-masterdata-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:17:13.827 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:17:13.894 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:17:13.897 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-masterdata-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-masterdata-service,current list of Servers=[************:20008],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:20008;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@9b1d682e
2024-12-17 16:17:50.505 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:17:51.606 [Thread-26] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-12-17 16:17:51.703 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-12-17 16:17:51.705 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-12-17 16:17:51.729 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-12-17 16:17:51.794 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-12-17 16:17:53.058 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-12-17 16:17:53.076 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-certificate-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-12-17 16:17:53.079 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-12-17 16:17:53.079 [Thread-42] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-12-17 16:17:53.080 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-12-17 16:17:53.081 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-12-17 16:17:53.082 [Thread-39] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-12-17 16:17:53.193 [Thread-42] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-12-17 16:18:21.151 [main] INFO  c.c.config.ErrorLogRollingPolicy - ErrorLogRollingPolicy init ........
2024-12-17 16:18:22.520 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-12-17 16:18:22.524 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-12-17 16:18:25.656 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='**************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-certificate-service-config', dataIds='null', group='DEFAULT_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-12-17 16:18:26.246 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-certificate-service-config, group is : DEFAULT_GROUP
2024-12-17 16:18:28.747 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-17 16:18:28.753 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-12-17 16:18:28.845 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 70ms. Found 0 repository interfaces.
2024-12-17 16:18:31.415 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-12-17 16:18:31.422 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-12-17 16:18:34.242 [main] INFO  org.reflections.Reflections - Reflections took 98 ms to scan 1 urls, producing 9 keys and 30 values 
2024-12-17 16:18:35.723 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:18:35.724 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:18:35.739 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@1300abf6
2024-12-17 16:18:35.794 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:18:36.282 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:18:36.295 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:18:36.542 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:18:36.545 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[**************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:**************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@451dda2d
2024-12-17 16:18:36.850 [main] ERROR c.c.c.s.i.c.w.WfFunctionConfig - registerFunction Exception,{}
feign.FeignException: status 500 reading FormFeignClient#getFormDefByCode(String)
	at feign.FeignException.errorStatus(FeignException.java:78)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:93)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:149)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:78)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:103)
	at com.sun.proxy.$Proxy155.getFormDefByCode(Unknown Source)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.doInitWfRegister(WfFunctionConfig.java:89)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.AbstractBeanFactory$$Lambda$158/0x0000000000000000.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:18:36.851 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-12-17 16:18:36.860 [main] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=null, canAlarm = true
2024-12-17 16:18:36.895 [main] INFO  com.caidaocloud.config.MailConfig - ------------------------ERROR log email sendHtmlMail fail,From address must not be null
java.lang.IllegalArgumentException: From address must not be null
	at org.springframework.util.Assert.notNull(Assert.java:198)
	at org.springframework.mail.javamail.MimeMessageHelper.setFrom(MimeMessageHelper.java:546)
	at com.caidaocloud.config.MailConfig.sendErrLogHtmlMail(MailConfig.java:83)
	at com.caidaocloud.config.MailConfig$$FastClassBySpringCGLIB$$4f67a738.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:684)
	at com.caidaocloud.config.MailConfig$$EnhancerBySpringCGLIB$$17759c42.sendErrLogHtmlMail(<generated>)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:35)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:13)
	at ch.qos.logback.core.AppenderBase.doAppend(AppenderBase.java:82)
	at ch.qos.logback.core.spi.AppenderAttachableImpl.appendLoopOnAppenders(AppenderAttachableImpl.java:51)
	at ch.qos.logback.classic.Logger.appendLoopOnAppenders(Logger.java:270)
	at ch.qos.logback.classic.Logger.callAppenders(Logger.java:257)
	at ch.qos.logback.classic.Logger.buildLoggingEventAndAppend(Logger.java:421)
	at ch.qos.logback.classic.Logger.filterAndLog_0_Or3Plus(Logger.java:383)
	at ch.qos.logback.classic.Logger.error(Logger.java:538)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:69)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at org.springframework.beans.factory.support.AbstractBeanFactory$$Lambda$158/0x0000000000000000.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:18:37.308 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:18:39.111 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:18:39.111 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:18:42.412 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-12-17 16:18:42.730 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for **************/**************:6379
2024-12-17 16:18:43.061 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for **************/**************:6379
2024-12-17 16:18:44.865 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateAndEmpStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@ed5d2cb[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAndEmpStatusTask#execute]
2024-12-17 16:18:44.866 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateAuthFieldTaskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@a27da88a[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAuthFieldTask#execute]
2024-12-17 16:18:44.866 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateExpireJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@19415527[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateExpireTask#notice]
2024-12-17 16:18:44.866 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6874b325[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#execute]
2024-12-17 16:18:44.866 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateProjectStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5ff5d440[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#projectStatusHandler]
2024-12-17 16:18:45.165 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-12-17 16:18:45.187 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-12-17 16:18:45.215 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-12-17 16:18:45.216 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-12-17 16:18:45.304 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-12-17 16:18:45.939 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_1
2024-12-17 16:18:45.959 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2024-12-17 16:18:45.963 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-12-17 16:18:45.996 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_2
2024-12-17 16:18:46.000 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-12-17 16:18:46.018 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_1
2024-12-17 16:18:46.027 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_3
2024-12-17 16:18:46.185 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_2
2024-12-17 16:18:46.190 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_1
2024-12-17 16:18:46.197 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_1
2024-12-17 16:18:46.211 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_4
2024-12-17 16:18:46.226 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_3
2024-12-17 16:18:46.230 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2024-12-17 16:18:46.233 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingPOST_1
2024-12-17 16:18:46.245 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_5
2024-12-17 16:18:46.248 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2024-12-17 16:18:46.250 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_1
2024-12-17 16:18:46.253 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2024-12-17 16:18:46.264 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageListUsingPOST_1
2024-12-17 16:18:46.274 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_2
2024-12-17 16:18:46.344 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2024-12-17 16:18:48.872 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:18:48.888 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-masterdata-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-masterdata-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:18:48.889 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:18:48.923 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:18:48.925 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-masterdata-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-masterdata-service,current list of Servers=[************:20008],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:20008;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@d8df0f7d
2024-12-17 16:18:56.524 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:18:56.761 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-12-17 16:18:56.761 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-12-17 16:18:56.762 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-12-17 16:18:56.762 [Thread-26] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-12-17 16:18:56.763 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-12-17 16:18:56.826 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-12-17 16:18:56.844 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-certificate-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-12-17 16:18:56.844 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-12-17 16:18:56.844 [Thread-42] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-12-17 16:18:56.845 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-12-17 16:18:56.846 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-12-17 16:18:56.846 [Thread-39] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-12-17 16:18:56.891 [Thread-42] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-12-17 16:19:16.405 [main] INFO  c.c.config.ErrorLogRollingPolicy - ErrorLogRollingPolicy init ........
2024-12-17 16:19:17.486 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-12-17 16:19:17.492 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-12-17 16:19:19.443 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='**************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-certificate-service-config', dataIds='null', group='DEFAULT_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-12-17 16:19:19.952 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-certificate-service-config, group is : DEFAULT_GROUP
2024-12-17 16:19:23.728 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-17 16:19:23.734 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-12-17 16:19:23.835 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 77ms. Found 0 repository interfaces.
2024-12-17 16:19:28.143 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-12-17 16:19:28.148 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-12-17 16:19:31.115 [main] INFO  org.reflections.Reflections - Reflections took 85 ms to scan 1 urls, producing 9 keys and 30 values 
2024-12-17 16:19:32.403 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:19:32.404 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:19:32.415 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@c6f31471
2024-12-17 16:19:32.450 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:19:32.856 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:19:32.866 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:19:33.079 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:19:33.082 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[**************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:**************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@c95d53a3
2024-12-17 16:19:33.384 [main] ERROR c.c.c.s.i.c.w.WfFunctionConfig - registerFunction Exception,{}
feign.FeignException: status 500 reading FormFeignClient#getFormDefByCode(String)
	at feign.FeignException.errorStatus(FeignException.java:78)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:93)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:149)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:78)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:103)
	at com.sun.proxy.$Proxy155.getFormDefByCode(Unknown Source)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.doInitWfRegister(WfFunctionConfig.java:89)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at <unknown class>.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:19:33.385 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-12-17 16:19:33.393 [main] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=null, canAlarm = true
2024-12-17 16:19:33.424 [main] INFO  com.caidaocloud.config.MailConfig - ------------------------ERROR log email sendHtmlMail fail,From address must not be null
java.lang.IllegalArgumentException: From address must not be null
	at org.springframework.util.Assert.notNull(Assert.java:198)
	at org.springframework.mail.javamail.MimeMessageHelper.setFrom(MimeMessageHelper.java:546)
	at com.caidaocloud.config.MailConfig.sendErrLogHtmlMail(MailConfig.java:83)
	at com.caidaocloud.config.MailConfig$$FastClassBySpringCGLIB$$4f67a738.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:684)
	at com.caidaocloud.config.MailConfig$$EnhancerBySpringCGLIB$$60e2895f.sendErrLogHtmlMail(<generated>)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:35)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:13)
	at ch.qos.logback.core.AppenderBase.doAppend(AppenderBase.java:82)
	at ch.qos.logback.core.spi.AppenderAttachableImpl.appendLoopOnAppenders(AppenderAttachableImpl.java:51)
	at ch.qos.logback.classic.Logger.appendLoopOnAppenders(Logger.java:270)
	at ch.qos.logback.classic.Logger.callAppenders(Logger.java:257)
	at ch.qos.logback.classic.Logger.buildLoggingEventAndAppend(Logger.java:421)
	at ch.qos.logback.classic.Logger.filterAndLog_0_Or3Plus(Logger.java:383)
	at ch.qos.logback.classic.Logger.error(Logger.java:538)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:69)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at <unknown class>.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:19:33.872 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:19:34.913 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:19:34.915 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:19:37.109 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-12-17 16:19:37.353 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for **************/**************:6379
2024-12-17 16:19:37.652 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for **************/**************:6379
2024-12-17 16:19:39.376 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateAndEmpStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@d0034f77[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAndEmpStatusTask#execute]
2024-12-17 16:19:39.377 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateAuthFieldTaskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2c521006[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAuthFieldTask#execute]
2024-12-17 16:19:39.377 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateExpireJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@b29e847[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateExpireTask#notice]
2024-12-17 16:19:39.377 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@f3d343e0[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#execute]
2024-12-17 16:19:39.377 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateProjectStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6db6168f[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#projectStatusHandler]
2024-12-17 16:19:39.616 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-12-17 16:19:39.635 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-12-17 16:19:39.667 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-12-17 16:19:39.668 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-12-17 16:19:39.741 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-12-17 16:19:40.255 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_1
2024-12-17 16:19:40.275 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2024-12-17 16:19:40.280 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-12-17 16:19:40.341 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_2
2024-12-17 16:19:40.345 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-12-17 16:19:40.360 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_1
2024-12-17 16:19:40.372 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_3
2024-12-17 16:19:40.522 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_2
2024-12-17 16:19:40.526 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_1
2024-12-17 16:19:40.531 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_1
2024-12-17 16:19:40.541 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_4
2024-12-17 16:19:40.551 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_3
2024-12-17 16:19:40.561 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2024-12-17 16:19:40.563 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingPOST_1
2024-12-17 16:19:40.574 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_5
2024-12-17 16:19:40.576 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2024-12-17 16:19:40.579 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_1
2024-12-17 16:19:40.582 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2024-12-17 16:19:40.591 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageListUsingPOST_1
2024-12-17 16:19:40.600 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_2
2024-12-17 16:19:40.661 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2024-12-17 16:19:43.981 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:19:43.986 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:19:43.988 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:19:44.025 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:19:44.026 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-service,current list of Servers=[************:10013],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:10013;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@3fa0ab9c
2024-12-17 16:19:49.701 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:22:13.019 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-12-17 16:22:13.018 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-12-17 16:22:13.018 [Thread-26] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-12-17 16:22:13.025 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-12-17 16:22:13.029 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-12-17 16:22:13.176 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-12-17 16:22:13.218 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-certificate-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-12-17 16:22:13.219 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-12-17 16:22:13.219 [Thread-42] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-12-17 16:22:13.220 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-12-17 16:22:13.221 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-12-17 16:22:13.221 [Thread-39] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-12-17 16:22:13.290 [Thread-42] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-12-17 16:30:50.868 [main] INFO  c.c.config.ErrorLogRollingPolicy - ErrorLogRollingPolicy init ........
2024-12-17 16:30:53.301 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-12-17 16:30:53.304 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-12-17 16:30:56.260 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='**************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-certificate-service-config', dataIds='null', group='DEFAULT_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-12-17 16:30:56.921 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-certificate-service-config, group is : DEFAULT_GROUP
2024-12-17 16:30:59.373 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-17 16:30:59.379 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-12-17 16:30:59.471 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 69ms. Found 0 repository interfaces.
2024-12-17 16:31:02.010 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-12-17 16:31:02.016 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-12-17 16:31:04.896 [main] INFO  org.reflections.Reflections - Reflections took 138 ms to scan 1 urls, producing 9 keys and 30 values 
2024-12-17 16:31:06.224 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:31:06.224 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:31:06.236 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@42c60fb9
2024-12-17 16:31:06.281 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:31:06.703 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:31:06.712 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:31:06.918 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:31:06.922 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[**************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:**************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@eef20fc0
2024-12-17 16:31:07.223 [main] ERROR c.c.c.s.i.c.w.WfFunctionConfig - registerFunction Exception,{}
feign.FeignException: status 500 reading FormFeignClient#getFormDefByCode(String)
	at feign.FeignException.errorStatus(FeignException.java:78)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:93)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:149)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:78)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:103)
	at com.sun.proxy.$Proxy155.getFormDefByCode(Unknown Source)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.doInitWfRegister(WfFunctionConfig.java:89)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at <unknown class>.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:31:07.224 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-12-17 16:31:07.232 [main] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=null, canAlarm = true
2024-12-17 16:31:07.259 [main] INFO  com.caidaocloud.config.MailConfig - ------------------------ERROR log email sendHtmlMail fail,From address must not be null
java.lang.IllegalArgumentException: From address must not be null
	at org.springframework.util.Assert.notNull(Assert.java:198)
	at org.springframework.mail.javamail.MimeMessageHelper.setFrom(MimeMessageHelper.java:546)
	at com.caidaocloud.config.MailConfig.sendErrLogHtmlMail(MailConfig.java:83)
	at com.caidaocloud.config.MailConfig$$FastClassBySpringCGLIB$$4f67a738.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:684)
	at com.caidaocloud.config.MailConfig$$EnhancerBySpringCGLIB$$4e01d0f0.sendErrLogHtmlMail(<generated>)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:35)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:13)
	at ch.qos.logback.core.AppenderBase.doAppend(AppenderBase.java:82)
	at ch.qos.logback.core.spi.AppenderAttachableImpl.appendLoopOnAppenders(AppenderAttachableImpl.java:51)
	at ch.qos.logback.classic.Logger.appendLoopOnAppenders(Logger.java:270)
	at ch.qos.logback.classic.Logger.callAppenders(Logger.java:257)
	at ch.qos.logback.classic.Logger.buildLoggingEventAndAppend(Logger.java:421)
	at ch.qos.logback.classic.Logger.filterAndLog_0_Or3Plus(Logger.java:383)
	at ch.qos.logback.classic.Logger.error(Logger.java:538)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:69)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at <unknown class>.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:31:07.724 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:31:09.082 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:31:09.083 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:31:11.569 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-12-17 16:31:11.827 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for **************/**************:6379
2024-12-17 16:31:12.139 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for **************/**************:6379
2024-12-17 16:31:14.160 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateAndEmpStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@ee322c7f[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAndEmpStatusTask#execute]
2024-12-17 16:31:14.160 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateAuthFieldTaskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@22b1e224[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAuthFieldTask#execute]
2024-12-17 16:31:14.160 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateExpireJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@288fab21[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateExpireTask#notice]
2024-12-17 16:31:14.160 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5654f3e8[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#execute]
2024-12-17 16:31:14.161 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateProjectStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@ca64ca9d[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#projectStatusHandler]
2024-12-17 16:31:14.419 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-12-17 16:31:14.443 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-12-17 16:31:14.467 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-12-17 16:31:14.467 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-12-17 16:31:14.565 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-12-17 16:31:15.199 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_1
2024-12-17 16:31:15.218 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2024-12-17 16:31:15.222 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-12-17 16:31:15.254 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_2
2024-12-17 16:31:15.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-12-17 16:31:15.283 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_1
2024-12-17 16:31:15.292 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_3
2024-12-17 16:31:15.429 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_2
2024-12-17 16:31:15.432 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_1
2024-12-17 16:31:15.440 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_1
2024-12-17 16:31:15.458 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_4
2024-12-17 16:31:15.478 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_3
2024-12-17 16:31:15.481 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2024-12-17 16:31:15.484 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingPOST_1
2024-12-17 16:31:15.494 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_5
2024-12-17 16:31:15.497 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2024-12-17 16:31:15.500 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_1
2024-12-17 16:31:15.505 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2024-12-17 16:31:15.516 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageListUsingPOST_1
2024-12-17 16:31:15.532 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_2
2024-12-17 16:31:15.608 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2024-12-17 16:31:18.794 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:31:18.798 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:31:18.800 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:31:18.834 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:31:18.835 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-service,current list of Servers=[************:10013],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:10013;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@dba7d24a
2024-12-17 16:31:34.377 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:35:37.868 [AMQP Connection **************:5672] WARN  c.r.c.impl.ForgivingExceptionHandler - An unexpected connection driver error occured (Exception message: Connection reset)
2024-12-17 16:37:52.476 [redisson-timer-7-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0xecb65433, L:/**************:61955 - R:**************/**************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://**************:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at <unknown class>.run(Unknown Source)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:826)
2024-12-17 16:37:52.482 [redisson-timer-7-1] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-12-17 16:38:21.864 [redisson-timer-7-1] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=null, canAlarm = true
2024-12-17 16:38:21.880 [redisson-timer-7-1] INFO  com.caidaocloud.config.MailConfig - ------------------------ERROR log email sendHtmlMail fail,From address must not be null
java.lang.IllegalArgumentException: From address must not be null
	at org.springframework.util.Assert.notNull(Assert.java:198)
	at org.springframework.mail.javamail.MimeMessageHelper.setFrom(MimeMessageHelper.java:546)
	at com.caidaocloud.config.MailConfig.sendErrLogHtmlMail(MailConfig.java:83)
	at com.caidaocloud.config.MailConfig$$FastClassBySpringCGLIB$$4f67a738.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:684)
	at com.caidaocloud.config.MailConfig$$EnhancerBySpringCGLIB$$4e01d0f0.sendErrLogHtmlMail(<generated>)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:35)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:13)
	at ch.qos.logback.core.AppenderBase.doAppend(AppenderBase.java:82)
	at ch.qos.logback.core.spi.AppenderAttachableImpl.appendLoopOnAppenders(AppenderAttachableImpl.java:51)
	at ch.qos.logback.classic.Logger.appendLoopOnAppenders(Logger.java:270)
	at ch.qos.logback.classic.Logger.callAppenders(Logger.java:257)
	at ch.qos.logback.classic.Logger.buildLoggingEventAndAppend(Logger.java:421)
	at ch.qos.logback.classic.Logger.filterAndLog_0_Or3Plus(Logger.java:383)
	at ch.qos.logback.classic.Logger.error(Logger.java:538)
	at org.redisson.client.handler.PingConnectionHandler.lambda$sendPing$1(PingConnectionHandler.java:87)
	at <unknown class>.run(Unknown Source)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:826)
2024-12-17 16:38:50.511 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-12-17 16:38:50.512 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-12-17 16:38:50.513 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-12-17 16:38:50.514 [Thread-26] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-12-17 16:38:50.518 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-12-17 16:38:50.639 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-12-17 16:38:50.652 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-certificate-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-12-17 16:38:50.652 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-12-17 16:38:50.652 [Thread-42] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-12-17 16:38:50.653 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-12-17 16:38:50.654 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-12-17 16:38:50.654 [Thread-39] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-12-17 16:38:50.705 [Thread-42] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-12-17 16:39:27.155 [main] INFO  c.c.config.ErrorLogRollingPolicy - ErrorLogRollingPolicy init ........
2024-12-17 16:39:28.288 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-12-17 16:39:28.293 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-12-17 16:39:30.947 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='**************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-certificate-service-config', dataIds='null', group='DEFAULT_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-12-17 16:39:31.574 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-certificate-service-config, group is : DEFAULT_GROUP
2024-12-17 16:39:35.425 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-17 16:39:35.431 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-12-17 16:39:35.507 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 58ms. Found 0 repository interfaces.
2024-12-17 16:39:38.039 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-12-17 16:39:38.047 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-12-17 16:39:41.703 [main] INFO  org.reflections.Reflections - Reflections took 108 ms to scan 1 urls, producing 9 keys and 30 values 
2024-12-17 16:39:43.135 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:39:43.136 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:39:43.149 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@eafcb6d9
2024-12-17 16:39:43.191 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:39:43.584 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:39:43.593 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:39:43.829 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:39:43.832 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[**************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:**************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@85a783a0
2024-12-17 16:39:44.178 [main] ERROR c.c.c.s.i.c.w.WfFunctionConfig - registerFunction Exception,{}
feign.FeignException: status 500 reading FormFeignClient#getFormDefByCode(String)
	at feign.FeignException.errorStatus(FeignException.java:78)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:93)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:149)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:78)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:103)
	at com.sun.proxy.$Proxy155.getFormDefByCode(Unknown Source)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.doInitWfRegister(WfFunctionConfig.java:89)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at <unknown class>.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:39:44.179 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-12-17 16:39:44.188 [main] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=null, canAlarm = true
2024-12-17 16:39:44.217 [main] INFO  com.caidaocloud.config.MailConfig - ------------------------ERROR log email sendHtmlMail fail,From address must not be null
java.lang.IllegalArgumentException: From address must not be null
	at org.springframework.util.Assert.notNull(Assert.java:198)
	at org.springframework.mail.javamail.MimeMessageHelper.setFrom(MimeMessageHelper.java:546)
	at com.caidaocloud.config.MailConfig.sendErrLogHtmlMail(MailConfig.java:83)
	at com.caidaocloud.config.MailConfig$$FastClassBySpringCGLIB$$4f67a738.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:684)
	at com.caidaocloud.config.MailConfig$$EnhancerBySpringCGLIB$$1d8da549.sendErrLogHtmlMail(<generated>)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:35)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:13)
	at ch.qos.logback.core.AppenderBase.doAppend(AppenderBase.java:82)
	at ch.qos.logback.core.spi.AppenderAttachableImpl.appendLoopOnAppenders(AppenderAttachableImpl.java:51)
	at ch.qos.logback.classic.Logger.appendLoopOnAppenders(Logger.java:270)
	at ch.qos.logback.classic.Logger.callAppenders(Logger.java:257)
	at ch.qos.logback.classic.Logger.buildLoggingEventAndAppend(Logger.java:421)
	at ch.qos.logback.classic.Logger.filterAndLog_0_Or3Plus(Logger.java:383)
	at ch.qos.logback.classic.Logger.error(Logger.java:538)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:69)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at <unknown class>.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:39:44.603 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:39:47.421 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:39:47.422 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:39:51.390 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-12-17 16:39:51.700 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for **************/**************:6379
2024-12-17 16:39:52.009 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for **************/**************:6379
2024-12-17 16:39:53.923 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateAndEmpStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2d706120[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAndEmpStatusTask#execute]
2024-12-17 16:39:53.924 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateAuthFieldTaskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@db9c5ac4[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAuthFieldTask#execute]
2024-12-17 16:39:53.924 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateExpireJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@d4080b6c[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateExpireTask#notice]
2024-12-17 16:39:53.924 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@a73641a2[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#execute]
2024-12-17 16:39:53.925 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateProjectStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@ec309fd0[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#projectStatusHandler]
2024-12-17 16:39:54.171 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-12-17 16:39:54.195 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-12-17 16:39:54.243 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-12-17 16:39:54.243 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-12-17 16:39:54.433 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-12-17 16:39:55.223 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_1
2024-12-17 16:39:55.247 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2024-12-17 16:39:55.251 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-12-17 16:39:55.283 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_2
2024-12-17 16:39:55.289 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-12-17 16:39:55.306 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_1
2024-12-17 16:39:55.312 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_3
2024-12-17 16:39:55.483 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_2
2024-12-17 16:39:55.491 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_1
2024-12-17 16:39:55.501 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_1
2024-12-17 16:39:55.526 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_4
2024-12-17 16:39:55.551 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_3
2024-12-17 16:39:55.555 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2024-12-17 16:39:55.563 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingPOST_1
2024-12-17 16:39:55.586 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_5
2024-12-17 16:39:55.593 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2024-12-17 16:39:55.597 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_1
2024-12-17 16:39:55.606 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2024-12-17 16:39:55.630 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageListUsingPOST_1
2024-12-17 16:39:55.649 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_2
2024-12-17 16:39:55.815 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2024-12-17 16:40:00.616 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:40:00.622 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:40:00.625 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:40:00.672 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:40:00.674 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-service,current list of Servers=[************:10013],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:10013;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@a3f8723b
2024-12-17 16:40:44.979 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:42:06.624 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-12-17 16:42:06.624 [Thread-26] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-12-17 16:42:06.623 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-12-17 16:42:06.629 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-12-17 16:42:06.635 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-12-17 16:42:06.770 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-12-17 16:42:06.785 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-certificate-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-12-17 16:42:06.786 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-12-17 16:42:06.786 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-12-17 16:42:06.787 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-12-17 16:42:06.787 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-12-17 16:42:06.788 [Thread-39] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-12-17 16:42:06.870 [Thread-43] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-12-17 16:47:26.729 [main] INFO  c.c.config.ErrorLogRollingPolicy - ErrorLogRollingPolicy init ........
2024-12-17 16:47:33.260 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-12-17 16:47:33.271 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-12-17 16:47:37.395 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='**************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-certificate-service-config', dataIds='null', group='DEFAULT_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-12-17 16:47:40.429 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-certificate-service-config, group is : DEFAULT_GROUP
2024-12-17 16:47:43.651 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-17 16:47:43.658 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-12-17 16:47:43.759 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 72ms. Found 0 repository interfaces.
2024-12-17 16:47:46.501 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-12-17 16:47:46.515 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-12-17 16:47:49.561 [main] INFO  org.reflections.Reflections - Reflections took 98 ms to scan 1 urls, producing 9 keys and 30 values 
2024-12-17 16:47:50.942 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:47:50.943 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:47:50.959 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@75b05073
2024-12-17 16:47:51.001 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:47:51.436 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:47:51.444 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:47:51.697 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:47:51.700 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[**************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:**************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@fc40e941
2024-12-17 16:47:52.237 [main] ERROR c.c.c.s.i.c.w.WfFunctionConfig - registerFunction Exception,{}
feign.FeignException: status 500 reading FormFeignClient#getFormDefByCode(String)
	at feign.FeignException.errorStatus(FeignException.java:78)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:93)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:149)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:78)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:103)
	at com.sun.proxy.$Proxy155.getFormDefByCode(Unknown Source)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.doInitWfRegister(WfFunctionConfig.java:89)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at <unknown class>.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:47:52.238 [main] INFO  c.c.config.ErrorLogRollingPolicy - send err mail event...
2024-12-17 16:47:52.246 [main] INFO  com.caidaocloud.config.MailConfig - sendHtmlMail title=null, canAlarm = true
2024-12-17 16:47:52.276 [main] INFO  com.caidaocloud.config.MailConfig - ------------------------ERROR log email sendHtmlMail fail,From address must not be null
java.lang.IllegalArgumentException: From address must not be null
	at org.springframework.util.Assert.notNull(Assert.java:198)
	at org.springframework.mail.javamail.MimeMessageHelper.setFrom(MimeMessageHelper.java:546)
	at com.caidaocloud.config.MailConfig.sendErrLogHtmlMail(MailConfig.java:83)
	at com.caidaocloud.config.MailConfig$$FastClassBySpringCGLIB$$4f67a738.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:684)
	at com.caidaocloud.config.MailConfig$$EnhancerBySpringCGLIB$$e2852efe.sendErrLogHtmlMail(<generated>)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:35)
	at com.caidaocloud.config.ErrorLogRollingPolicy.append(ErrorLogRollingPolicy.java:13)
	at ch.qos.logback.core.AppenderBase.doAppend(AppenderBase.java:82)
	at ch.qos.logback.core.spi.AppenderAttachableImpl.appendLoopOnAppenders(AppenderAttachableImpl.java:51)
	at ch.qos.logback.classic.Logger.appendLoopOnAppenders(Logger.java:270)
	at ch.qos.logback.classic.Logger.callAppenders(Logger.java:257)
	at ch.qos.logback.classic.Logger.buildLoggingEventAndAppend(Logger.java:421)
	at ch.qos.logback.classic.Logger.filterAndLog_0_Or3Plus(Logger.java:383)
	at ch.qos.logback.classic.Logger.error(Logger.java:538)
	at com.caidaocloud.certificate.service.infrastructure.config.workflow.WfFunctionConfig.initWfRegister(WfFunctionConfig.java:69)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:307)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:136)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1737)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:576)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:498)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320)
	at <unknown class>.getObject(Unknown Source)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:846)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:863)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:127)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:117)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:108)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2024-12-17 16:47:52.452 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:47:54.168 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-12-17 16:47:54.168 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-12-17 16:47:57.293 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-12-17 16:47:57.616 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for **************/**************:6379
2024-12-17 16:47:57.920 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for **************/**************:6379
2024-12-17 16:47:59.777 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateAndEmpStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@89e202d[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAndEmpStatusTask#execute]
2024-12-17 16:47:59.777 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateAuthFieldTaskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6cdf6993[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateAuthFieldTask#execute]
2024-12-17 16:47:59.778 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CertificateExpireJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@ecb5351c[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateExpireTask#notice]
2024-12-17 16:47:59.778 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@d6d76f04[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#execute]
2024-12-17 16:47:59.779 [main] INFO  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:certificateProjectStatusJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@8acb0ecf[class com.caidaocloud.certificate.service.certificate.application.cron.CertificateStatusTask#projectStatusHandler]
2024-12-17 16:48:00.061 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2024-12-17 16:48:00.081 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2024-12-17 16:48:00.103 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2024-12-17 16:48:00.108 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-12-17 16:48:00.192 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2024-12-17 16:48:00.819 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_1
2024-12-17 16:48:00.847 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2024-12-17 16:48:00.851 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2024-12-17 16:48:00.877 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_2
2024-12-17 16:48:00.881 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2024-12-17 16:48:00.895 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_1
2024-12-17 16:48:00.902 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_3
2024-12-17 16:48:01.027 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_2
2024-12-17 16:48:01.032 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_1
2024-12-17 16:48:01.038 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUsingPOST_1
2024-12-17 16:48:01.048 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_4
2024-12-17 16:48:01.062 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_3
2024-12-17 16:48:01.066 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2024-12-17 16:48:01.069 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getDetailUsingPOST_1
2024-12-17 16:48:01.078 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: selectListUsingPOST_5
2024-12-17 16:48:01.081 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2024-12-17 16:48:01.085 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateStatusUsingPOST_1
2024-12-17 16:48:01.089 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2024-12-17 16:48:01.104 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageListUsingPOST_1
2024-12-17 16:48:01.133 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: delUsingPOST_2
2024-12-17 16:48:01.244 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2024-12-17 16:48:05.131 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:48:05.135 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-12-17 16:48:05.137 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-12-17 16:48:05.174 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 16:48:05.176 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-service,current list of Servers=[************:10013],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:10013;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@14bd26dd
2024-12-17 16:49:02.235 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-12-17 17:18:47.976 [AMQP Connection **************:5672] WARN  c.r.c.impl.ForgivingExceptionHandler - An unexpected connection driver error occured (Exception message: Connection reset)
2024-12-17 17:18:47.982 [Thread-26] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-12-17 17:18:47.987 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-12-17 17:18:47.988 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-12-17 17:18:47.989 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-12-17 17:18:47.997 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-12-17 17:18:48.222 [Thread-40] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-12-17 17:18:48.264 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-certificate-service-local', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-12-17 17:18:48.264 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2024-12-17 17:18:48.265 [Thread-42] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2024-12-17 17:18:48.270 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2024-12-17 17:18:48.272 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2024-12-17 17:18:48.273 [Thread-39] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-12-17 17:18:48.320 [Thread-42] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
