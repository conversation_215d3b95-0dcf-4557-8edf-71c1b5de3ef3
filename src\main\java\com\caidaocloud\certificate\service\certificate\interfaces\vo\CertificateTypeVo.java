package com.caidaocloud.certificate.service.certificate.interfaces.vo;

import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 **/
@Data
@Slf4j
public class CertificateTypeVo extends DataEntity {

    @ApiModelProperty("证书名称")
    private String name;
    @ApiModelProperty("多语言证书名称")
    private Map<String,Object> i18nName;
    @ApiModelProperty("证书编码")
    private String code;
    @ApiModelProperty("层级")
    private Integer level;
    @ApiModelProperty("序列编码")
    private Integer sortNum;
    @ApiModelProperty("状态")
    private EnumSimple status;
    @ApiModelProperty("子集")
    private List<CertificateTypeVo> children;
    @ApiModelProperty("父id")
    private String pBid;

}
