package com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl;

import java.util.Optional;

import com.caidaocloud.certificate.service.certificate.domain.repository.IOrgRepository;
import com.caidaocloud.hrpaas.metadata.sdk.query.MdDataQuery;
import com.caidaocloud.masterdata.entity.org.OrgEntity;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2024/4/19
 */
@Repository
public class OrgRepositoryImpl implements IOrgRepository {
	@Override
	public Optional<OrgEntity> loadOrg(String organize) {
		return Optional.ofNullable(MdDataQuery.identifier(OrgEntity.ORG_IDENTIFIER).queryInvisible().specifyLanguage()
				.decrypt()
				.oneOrNull(organize, OrgEntity.class));
	}
}
