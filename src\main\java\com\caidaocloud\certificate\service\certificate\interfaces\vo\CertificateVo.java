package com.caidaocloud.certificate.service.certificate.interfaces.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import groovy.util.logging.Slf4j;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:33
 * 证书
 **/
@Data
@Slf4j
@Service
public class CertificateVo  {

    private String bid;
    /**
     * 名称
     */
    private String name;
    /**
     * 编码
     */
    private String code;
    /**
     * 序列编码
     */
    private Integer sortNum;
    /**
     * 状态
     */
    private EnumSimple status;
    /**
     * 证书类型
     */
    private String typeBid;
    /**
     * 证书名称多语言
     */
    private Map<String,Object> i18nName;
    /**
     * 员工id
     */
    private String empId;
    /**
     * 项目id
     */
    private String proBid;

}
