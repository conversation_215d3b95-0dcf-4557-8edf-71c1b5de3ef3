package com.caidaocloud.certificate.service.certificate.interfaces.dto;

import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.hr.workflow.dto.WfAttachmentDto;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/6 10:57
 **/
@Data
@Service
@Slf4j
public class CeritificateAndEmpHistoryDatailDto extends DataEntity {



    @ApiModelProperty("员工工号")
    private String workNo;

    @ApiModelProperty("员工姓名")
    private String workName;

    @ApiModelProperty("申请人")
    private String applyName;

    @ApiModelProperty("发起人")
    private String launchName;

    @ApiModelProperty("证书类型")
    private String typeName;



    @ApiModelProperty("证书子类类型")
    private String typeSubName;



    @ApiModelProperty("证书名称")
    private String ceritifiCateName;

    @ApiModelProperty("证书编码")
    private String ceritifiCateCode;



    @ApiModelProperty("注册日期")
    private String registerTime;

    @ApiModelProperty("失效日期")
    private String expireTime;

    @ApiModelProperty("注册号")
    private String registrationNo;

    @ApiModelProperty("专业")
    private String specialty;

    @ApiModelProperty("专业多语言字段")
    private Map<String,Object> i18nName;

    @ApiModelProperty("签发地")
    private String issueAt;

    @ApiModelProperty("入省备案")
    private String accessProvince;

    @ApiModelProperty("是否限制使用")
    private String isuse;

    @ApiModelProperty("是否注册")
    private String isRegister;

    @ApiModelProperty("备注")
    private String remake;

    @ApiModelProperty("附件")
    private List<WfAttachmentDto> file;

    @ApiModelProperty("流程key")
    private String businessKey;

}
