package com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl;

import java.util.List;

import com.caidaocloud.certificate.CertificateApplication;
import com.caidaocloud.certificate.service.certificate.application.cron.CertificateAndEmpStatusTask;
import com.caidaocloud.certificate.service.certificate.application.service.CeritificateAndEmpService;
import com.caidaocloud.certificate.service.certificate.domain.entity.CeritificateAndEmpDo;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateDo;
import com.caidaocloud.certificate.service.certificate.domain.repository.ICertificateRepository;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CeritificateAndEmpQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.facade.CeritificateAndEmpController;
import com.caidaocloud.certificate.service.certificate.interfaces.facade.QualifiedPerController;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.junit.Assert.*;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/5/22
 */
@SpringBootTest(classes = CertificateApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class CeritificateAndEmpRepositoryImplTests {

	@Autowired
	private CeritificateAndEmpService ceritificateAndEmpService;

	@Autowired
	private ICertificateRepository certificateRepository;

	@Before
	public void setUp() {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setEmpId(0L);
		userInfo.setUserId(0L);
		userInfo.setTenantId("11");
		SecurityUserUtil.setSecurityUserInfo(userInfo);
	}

	@Test
	public void  joinTest(){
		log.info("join result:{}", FastjsonUtil.toJson(new CeritificateAndEmpRepositoryImpl().basicJoinPage(new CeritificateAndEmpQueryDto(), 10, 1)));

		new CeritificateAndEmpRepositoryImpl().queryTablePage(new CeritificateAndEmpQueryDto(), 10, 1);
	}

	@Test
	public void test(){
		CeritificateAndEmpQueryDto dto = FastjsonUtil.toObject("{\"pageNo\":1,\"pageSize\":10,\"nameOrNo\":\"11223\",\"filterAvailable\":false}", CeritificateAndEmpQueryDto.class);
		SpringUtil.getBean(QualifiedPerController.class).pageList(dto, new MockHttpServletRequest());
	}

	@Test
	public void empTest(){
		CeritificateAndEmpQueryDto dto = new CeritificateAndEmpQueryDto();
		dto.setEmpId("2135174506035200");
		SpringUtil.getBean(CeritificateAndEmpController.class).getById(dto);
	}


	@SneakyThrows
	@Test
	public  void status_test(){
		CeritificateAndEmpQueryDto dto=new CeritificateAndEmpQueryDto();
		int pageNo =1;
		while (true) {
			List<CeritificateAndEmpDo> page = ceritificateAndEmpService.queryPage(dto, 1000, pageNo++);
			if (page.isEmpty()) {
				break;
			}
			for (CeritificateAndEmpDo data : page) {
				ceritificateAndEmpService.flush2(data);
			}
		}

	}
	//
	// @Test
	// public void crt(){
	// 		certificateRepository.pageList(new CertificateQueryDto(), CertificateDo.IDENTIFIER);
	// }
}