package com.caidaocloud.certificate.service.certificate.application.service;

import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateEmpDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/5/20 16:27
 **/
@Service
@Slf4j
@Deprecated
public class CertificateEmpSettingService {

    @Resource
    private  CertificateEmpDo  CertificateEmpDo;

    public List< CertificateEmpDo> selectList( CertificateEmpDo dto) {
       return  CertificateEmpDo.selectList(dto);
    }

    public void add( List<CertificateEmpDo> dtos) {
          CertificateEmpDo.save(dtos);
    }

    public void del( CertificateEmpDo dto) {
          CertificateEmpDo.delete(dto);
    }

}
