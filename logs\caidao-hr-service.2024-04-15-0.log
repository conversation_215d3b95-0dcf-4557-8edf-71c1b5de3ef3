2024-04-15 10:58:05.609 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-04-15 10:58:05.613 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-04-15 10:58:06.490 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='**************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-certificate-service-config', dataIds='null', group='DEFAULT_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-04-15 10:58:06.673 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-certificate-service-config, group is : DEFAULT_GROUP
2024-04-15 10:58:07.277 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-04-15 10:58:07.279 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-04-15 10:58:07.301 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10ms. Found 0 repository interfaces.
2024-04-15 10:58:07.875 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-04-15 10:58:08.136 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-04-15 10:58:08.764 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2024-04-15 10:58:08.770 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-04-15 10:58:08.770 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2024-04-15 10:58:08.774 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
2024-04-15 10:58:08.846 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-04-15 10:58:09.005 [main] INFO  org.reflections.Reflections - Reflections took 39 ms to scan 1 urls, producing 9 keys and 28 values 
2024-04-15 10:58:09.318 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-04-15 10:58:09.318 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-04-15 10:58:09.321 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-04-15 10:58:09.321 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-04-15 10:58:10.796 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-04-15 10:58:10.931 [redisson-netty-5-13] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for **************/**************:6379
2024-04-15 10:58:11.512 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for **************/**************:6379
2024-04-15 10:58:12.363 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2024-04-15 10:58:12.364 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2024-04-15 10:58:12.531 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, caidaocloud-certificate-service_1 **************:8080 register finished
2024-04-15 10:58:49.923 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-04-15 10:58:50.425 [http-nio-8080-exec-1] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-04-15 10:58:50.470 [http-nio-8080-exec-1] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-04-15 10:58:50.474 [http-nio-8080-exec-1] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-04-15 10:58:50.557 [http-nio-8080-exec-1] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-04-15 10:58:50.558 [http-nio-8080-exec-1] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[**************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:**************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@29ac538e
2024-04-15 10:58:51.482 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-04-15 11:19:11.142 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2024-04-15 11:19:11.145 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2024-04-15 11:19:11.867 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='**************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-certificate-service-config', dataIds='null', group='DEFAULT_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2024-04-15 11:19:12.040 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-certificate-service-config, group is : DEFAULT_GROUP
2024-04-15 11:19:12.609 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-04-15 11:19:12.616 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2024-04-15 11:19:12.636 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9ms. Found 0 repository interfaces.
2024-04-15 11:19:13.207 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-04-15 11:19:13.516 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-04-15 11:19:14.098 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2024-04-15 11:19:14.103 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-04-15 11:19:14.103 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2024-04-15 11:19:14.108 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
2024-04-15 11:19:14.184 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-04-15 11:19:14.333 [main] INFO  org.reflections.Reflections - Reflections took 29 ms to scan 1 urls, producing 9 keys and 28 values 
2024-04-15 11:19:14.606 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-04-15 11:19:14.606 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-04-15 11:19:14.609 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2024-04-15 11:19:14.609 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-04-15 11:19:15.734 [Thread-55] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2024-04-15 11:19:15.734 [Thread-35] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2024-04-15 11:19:15.735 [Thread-35] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2024-04-15 11:19:15.736 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2024-04-15 11:19:15.740 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2024-04-15 11:19:15.801 [Thread-45] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2024-04-15 11:19:15.816 [Thread-45] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2024-04-15 11:19:15.820 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2024-04-15 11:19:16.133 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-04-15 11:19:16.268 [redisson-netty-5-11] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for **************/**************:6379
2024-04-15 11:19:16.769 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for **************/**************:6379
2024-04-15 11:19:17.849 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2024-04-15 11:19:17.851 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2024-04-15 11:19:17.929 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, caidaocloud-certificate-service_1 **************:8080 register finished
2024-04-15 11:19:37.358 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-04-15 11:19:37.790 [http-nio-8080-exec-1] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-04-15 11:19:37.830 [http-nio-8080-exec-1] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2024-04-15 11:19:37.834 [http-nio-8080-exec-1] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2024-04-15 11:19:37.872 [http-nio-8080-exec-1] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2024-04-15 11:19:37.873 [http-nio-8080-exec-1] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[**************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:**************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@35400777
2024-04-15 11:19:38.841 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
