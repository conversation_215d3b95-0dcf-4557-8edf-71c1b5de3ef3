package com.caidaocloud.certificate.service.certificate.interfaces.facade;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/7/11 18:15
 **/
@RestController
@RequestMapping("/api/certificate/v1/redis")
@Api(value = "/api/certificate/v1/redis", description = "redis")
@Slf4j
public class RedisTempleController {
    @Autowired
    private RedisTemplate  redisTemplate;
    @PostMapping("/del")
    @ApiOperation("redis删除")
    public Set del(@RequestParam("pre") String pre){
        Set<String> keys = redisTemplate.keys(pre+"*");
        for (String key : keys) {
            redisTemplate.delete(key);
        }
        return  keys;

    }
}
