package com.caidaocloud.certificate.service.certificate.domain.base.enums;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

/**
 * 组织模块-数据业务状态
 *
 * <AUTHOR>
 * @Date 2021/12/3
 */
public enum StatusEnum {
    ENABLED(0, "已启用"),
    DEACTIVATED(1, "已停用");

    private Integer index;
    private String name;

    StatusEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public static String getName(Integer index) {
        for (StatusEnum c : StatusEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public String realValue(){
        return index.toString();
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static boolean isEnable(EnumSimple status) {
        if (status == null) {
            return false;
        }
        return ENABLED.getIndex().toString().equals(status.getValue());
    }
}
