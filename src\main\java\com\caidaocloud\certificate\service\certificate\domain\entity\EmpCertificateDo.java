package com.caidaocloud.certificate.service.certificate.domain.entity;

import com.caidaocloud.certificate.service.certificate.domain.base.entity.DataEntity;
import com.caidaocloud.certificate.service.certificate.domain.base.util.UserContext;
import com.caidaocloud.certificate.service.certificate.domain.repository.IEmpCertificateRepository;
import com.caidaocloud.certificate.service.certificate.infrastructure.respository.impl.CertificateRepositoryImpl;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateTypeQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.SetTopSaveDto;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CeritificateTopVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/22 14:22
 **/
@Data
@Repository
@Slf4j
public class EmpCertificateDo extends DataEntity {
    @Autowired
    private CertificateRepositoryImpl certificateRepository;
    @Autowired
    private  CertificateTypeDo certificateTypeDo;
    
    public static final String IDENTIFIER="entity.certificate.empCertificate";
    //用户id
    private String empid;
    //这是一个存取 证书头的json
    private String amount;

    public String save(SetTopSaveDto dto) {
        EmpCertificateDo data=new EmpCertificateDo();
        data.setAmount(dto.getJson());
        data.setEmpid(dto.getEmpId());
        data.setBid(SnowUtil.nextId());


        data.setCreateBy(UserContext.getUserId());
        data.setCreateTime(System.currentTimeMillis());
        data.setUpdateBy(UserContext.getUserId());
        data.setUpdateTime(System.currentTimeMillis());
        data.setIdentifier(IDENTIFIER);
        data.setDeleted(Boolean.FALSE);
        data.setTenantId(UserContext.getTenantId());
        return DataInsert.identifier(IDENTIFIER).insert(data);
    }

    public EmpCertificateDo selectById(String bid) {
        return SpringUtil.getBean(IEmpCertificateRepository.class).selectById(bid);
    }

    public List<CeritificateTopVo> query(String userId) {
        List<CeritificateTopVo> topVos=new ArrayList<>();

        List<EmpCertificateDo> query = SpringUtil.getBean(IEmpCertificateRepository.class).query(userId);
        if (query.size()<=0){
            return topVos;
        }
        EmpCertificateDo empCertificateDo = query.get(0);
        String amount = empCertificateDo.getAmount();
        String[] split = amount.split(",");
        List<String> collect = Arrays.stream(split).collect(Collectors.toList());
        List<CertificateDo> certificateDos = certificateRepository.selectListByIds(collect);

        List<CertificateTypeDo> typeDoList = certificateTypeDo.selectList(new CertificateTypeQueryDto());
        Map<String, CertificateTypeDo> typeDoMap = typeDoList.stream()
                .collect(Collectors.toMap(AbstractData::getBid, obj -> obj));


        Map<String, List<CertificateDo>> collect1 = certificateDos.stream().collect(Collectors.groupingBy(CertificateDo::getTypeBid));
        for (Map.Entry<String, List<CertificateDo>> stringListEntry : collect1.entrySet()) {
            CeritificateTopVo topTypeVo=new CeritificateTopVo();

            String typeBid = stringListEntry.getKey();
            CertificateTypeDo typeDo = typeDoMap.get(typeBid);
            topTypeVo.setName(typeDo.getName());
            topTypeVo.setBid(typeDo.getBid());
            topTypeVo.setChecked(true);

            List<CeritificateTopVo> topCerVos=new ArrayList<>();
            for (CertificateDo aDo : stringListEntry.getValue()) {
                CeritificateTopVo topCerVo=new CeritificateTopVo();
                topCerVo.setName(aDo.getName());
                topCerVo.setBid(aDo.getBid());
                topCerVo.setChecked(true);
                topCerVos.add(topCerVo);

            }
            topTypeVo.setChildren(topCerVos);

            topVos.add(topTypeVo);
        }
        return topVos;
    }

    public void remove(String bid) {
        DataDelete.identifier(IDENTIFIER).delete(bid);
    }
}
