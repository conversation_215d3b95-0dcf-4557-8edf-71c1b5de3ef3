package com.caidaocloud.certificate.service.certificate.interfaces.facade;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.caidaocloud.certificate.service.certificate.application.service.CertificateService;
import com.caidaocloud.certificate.service.certificate.domain.base.dto.StatusOptDto;
import com.caidaocloud.certificate.service.certificate.domain.base.enums.StatusEnum;
import com.caidaocloud.certificate.service.certificate.domain.base.util.*;
import com.caidaocloud.certificate.service.certificate.domain.entity.CertificateDo;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateDto;
import com.caidaocloud.certificate.service.certificate.interfaces.dto.CertificateQueryDto;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateExcelVo;
import com.caidaocloud.certificate.service.certificate.interfaces.vo.CertificateVo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CreateName: Mr.Li
 * CreateTime: 2024/4/9 17:44
 * 证书
 **/
@RestController
@RequestMapping("/api/certificate/v1")
@Api(value = "/api/certificate/v1", description = "证书类型管理", tags = "v0.1")
@Slf4j
public class CertificateController {

    @Autowired
    private CertificateService certificateService;

    private void preCheckArgument(CertificateDto dto) {
        PreCheck.preCheckArgument(StringUtils.isBlank(dto.getName()), "证书名称为空");
        PreCheck.preCheckArgument(null == dto.getCode(), "证书代码为空");
    }

    @PostMapping("/list")
    @ApiOperation("列表")
    public Result selectList(@RequestBody CertificateQueryDto dto) {
        List<CertificateDo> list = certificateService.selectList(dto);
        return Result.ok(list);
    }

    @ApiOperation("分页查询")
    @PostMapping("/pageList")
    public Result<PageResult<CertificateVo>> pageList(@RequestBody CertificateQueryDto queryDto) {
        PageResult<CertificateDo> pageList = certificateService.pageList(queryDto);
        List<CertificateDo> doList = pageList.getItems();
        List<CertificateVo> voList = ObjectConvertUtil.convertList(doList, CertificateVo.class, (t1, v1) -> {
            if (t1.getI18nName() != null) {
                v1.setI18nName(FastjsonUtil.toObject(t1.getI18nName(), Map.class));
            } else {
                Map<String, Object> i18nName = new HashMap<>();
                i18nName.put("default", t1.getName());
                v1.setI18nName(i18nName);
            }

        });
        return Result.ok(new PageResult<>(voList, pageList.getPageNo(), pageList.getPageSize(), pageList.getTotal()));
    }

    @ApiOperation("分页查询")
    @PostMapping("/dropList")
    public Result<List<CertificateVo>> dropList(@RequestBody CertificateQueryDto queryDto) {
        queryDto.setPageSize(-1);
        PageResult<CertificateDo> pageList = certificateService.pageList(queryDto);
        List<CertificateDo> doList = pageList.getItems();
        List<CertificateVo> voList = ObjectConvertUtil.convertList(doList, CertificateVo.class, (t1, v1) -> {
            if (t1.getI18nName() != null) {
                v1.setI18nName(FastjsonUtil.toObject(t1.getI18nName(), Map.class));
            } else {
                Map<String, Object> i18nName = new HashMap<>();
                i18nName.put("default", t1.getName());
                v1.setI18nName(i18nName);
            }

        });
        return Result.ok(voList);
    }

    @PostMapping("/add")
    @ApiOperation("新增")
    public Result add(@RequestBody CertificateDto dto) {
        // PreCheck.preCheckArgument(null == dto.getBid(), "bid为空");
        PreCheck.preCheckArgument(null == dto.getTypeBid(), "证书类型为空");
        preCheckArgument(dto);
        CertificateDo data = ObjectConverter.convert(dto, CertificateDo.class);
        data.setI18nName(LangUtil.getI18nValue(data.getName(), dto.getI18nName()));
        return Result.ok(certificateService.add(data));
    }

    @PostMapping("/edit")
    @ApiOperation("修改")
    public Result update(@RequestBody CertificateDto dto) {
        PreCheck.preCheckArgument(null == dto.getBid(), "bid为空");
        preCheckArgument(dto);
        CertificateDo data = ObjectConverter.convert(dto, CertificateDo.class);
        data.setI18nName(LangUtil.getI18nValue(data.getName(), dto.getI18nName()));
        certificateService.update(data);
        return Result.ok(true);
    }

    @ApiOperation("查看职务详情")
    @PostMapping("/getById")
    public Result getDetail(@RequestBody CertificateDto dto) {
        PreCheck.preCheckArgument(null == dto.getBid(), "bid为空");
        CertificateDo data = certificateService.getById(dto.getBid());
        CertificateDto datavo = ObjectConvertUtil.convert(data, CertificateDto.class,
                (it, v1) -> {
                    v1.setI18nName(FastjsonUtil.toObject(it.getI18nName(), Map.class));
                });
        return ResponseWrap.wrapResult(datavo);
    }

    @ApiOperation("删除证书类型")
    @PostMapping("/delete")
    public Result delete(@RequestBody CertificateDto dto) {
        PreCheck.preCheckArgument(null == dto.getBid(), "bid为空");
        CertificateDo data = new CertificateDo();
        data.setBid(dto.getBid());
        certificateService.delete(data);
        return Result.ok(true);
    }

    @ApiOperation("启用或停用职务")
    @PostMapping("/updateStatus")
    public Result updateStatus(@RequestBody StatusOptDto dto) {
        dto.preCheckArgument();
        CertificateDo data = ObjectConverter.convert(dto, CertificateDo.class);
        if (StatusEnum.ENABLED.getIndex().equals(dto.getStatus())) {
            certificateService.enable(data);
        } else {
            certificateService.disable(data);
        }
        return Result.ok(true);
    }

    @PostMapping("/importFile")
    @ApiOperation("导入")
    public Result importFile(@RequestParam("file") MultipartFile multipartFile) {
        UserInfo userInfo = UserContext.preCheckUser();
        List<String> importFailGrowthRecordIdList = certificateService.importFile(multipartFile,
                userInfo.getTenantId());
        return Result.ok(importFailGrowthRecordIdList);
    }

    @PostMapping("/export")
    @ApiOperation("导出")
    public void export(@RequestBody CertificateQueryDto queryDto, HttpServletResponse response) {
        // 直接使用CertificateQueryDto，与pageList保持一致的查询逻辑
        List<CertificateExcelVo> exportList = certificateService.selectExportRecordPage(queryDto);
        List<ExcelExportEntity> colList = new ArrayList<>();
        for (TagProperty tagProperty : certificateService.installCertificateHeader()) {
            ExcelExportEntity exprortEntity = new ExcelExportEntity(tagProperty.getPropertyTxt(),
                    tagProperty.getProperty(), 15);
            exprortEntity.setOrderNum(tagProperty.getOrder());
            colList.add(exprortEntity);
        }
        try {
            ExcelUtils.downloadDataListMapExcel(colList,
                    CollectionUtils.isEmpty(exportList) ? Lists.newArrayList() : exportList, "证书", response);
        } catch (Exception e) {
            log.error("download approval List excel err.{}", e.getMessage(), e);
        }
    }

    @PostMapping("/exportForExample")
    @ApiOperation("导入模版")
    public void exportForExample(@RequestBody CertificateDto dto, HttpServletResponse response) {
        List<ExcelExportEntity> colList = new ArrayList<>();
        for (TagProperty tagProperty : certificateService.installCertificateHeader()) {
            ExcelExportEntity exprortEntity = new ExcelExportEntity(tagProperty.getPropertyTxt(),
                    tagProperty.getProperty(), 15);
            exprortEntity.setOrderNum(tagProperty.getOrder());
            colList.add(exprortEntity);
        }
        try {
            ExcelUtils.downloadDataListMapExcel(colList, Lists.newArrayList(), "证书", response);
        } catch (Exception e) {
            log.error("download approval List excel err.{}", e.getMessage(), e);
        }

    }
}
