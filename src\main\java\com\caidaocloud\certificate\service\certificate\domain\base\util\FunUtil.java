package com.caidaocloud.certificate.service.certificate.domain.base.util;

import java.util.Optional;
import java.util.function.Function;

public abstract class FunUtil {
    public static <T, R> R getValue(T r, Function<? super T, ? extends R> function) {
        //如果 r = null, 调用map时 会自动返回Null，不会出现空指针
        return Optional.ofNullable(r).map(function).orElse(null);
    }

    public static <T> boolean isNotNull(T r) {
        //如果 r = null,会自动返回Null，不会出现空指针
        return Optional.ofNullable(r).isPresent();
    }

    public static <T> boolean isNull(T r) {
        return !isNotNull(r);
    }

    public static <T, R> boolean valueNotNull(T r, Function<? super T, ? extends R> function) {
        //如果 r = null, 调用map时 会自动返回Null，不会出现空指针
        return Optional.ofNullable(r).map(function).orElse(null) != null;
    }

    public static <T, R, E> boolean valueNotNull(T r, Function<? super T, ? extends R> function,
        Function<? super R, ? extends E> function2) {
        //如果 r = null, 调用map时 会自动返回Null，不会出现空指针
        return Optional.ofNullable(r).map(function).map(function2).orElse(null) != null;
    }

    public static String getOrDefault(String value, String defaultValue){
        if(null == value || "".equals(value)){
            return defaultValue;
        }

        return value;
    }
}